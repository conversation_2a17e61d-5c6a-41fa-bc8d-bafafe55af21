# For more information: https://laravel.com/docs/sail
services:
    invato-basis:
        build:
            context: ./docker/8.3
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
        image: sail-8.3/app
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        labels:
            # Traefik settings
            - 'traefik.enable=true'
            # Here we have to define the URL
            # More details https://doc.traefik.io/traefik/v2.0/routing/routers/#rule
            - 'traefik.http.routers.${TRAEFIK_HOST}.rule=HostRegexp(`${TRAEFIK_HOST}.docker.localhost`, `{subdomain:[a-z]+}.${TRAEFIK_HOST}.docker.localhost`)'
            # Here we define in wich network treafik can find this service
            - 'traefik.docker.network=web'
            # This is the port that traefik should proxy
            - 'traefik.http.services.${TRAEFIK_HOST}.loadbalancer.server.port=80'
            # Activation of TLS
            - "traefik.http.routers.${TRAEFIK_HOST}.tls=true"
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
        volumes:
            - '.:/var/www/html'
            - '~/.composer/auth.json:/home/<USER>/.composer/auth.json:delegated'
            - './docker/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh'
        networks:
            - sail
            - web
        depends_on:
            - mysql
            - redis
        restart:
            unless-stopped
    mysql:
        image: 'mysql/mysql-server:8.0'
        ports:
            - '${FORWARD_DB_PORT:-3306}:3306'
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ROOT_HOST: "%"
            MYSQL_DATABASE: '${DB_DATABASE}'
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ALLOW_EMPTY_PASSWORD: 1
        volumes:
            - 'sail-mysql:/var/lib/mysql'
            - './docker/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh'
        networks:
            - sail
        healthcheck:
            test: [ "CMD", "mysqladmin", "ping", "-p${DB_PASSWORD}" ]
            retries: 3
            timeout: 5s
        restart:
            unless-stopped
    redis:
        image: 'redis:alpine'
        volumes:
            - 'sail-redis:/data'
        networks:
            - sail
        healthcheck:
            test: [ "CMD", "redis-cli", "ping" ]
            retries: 3
            timeout: 5s
        restart:
            unless-stopped
networks:
    sail:
        driver: bridge
    web:
        external: true
volumes:
    sail-mysql:
        driver: local
    sail-redis:
        driver: local
