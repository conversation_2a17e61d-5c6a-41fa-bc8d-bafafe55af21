# Project Installatiehandleiding

Welkom bij ons project! Deze handleiding helpt je bij het opzetten van de omgeving, zodat je alles hebt wat je nodig
hebt om aan de slag te gaan met onze Docker-gebaseerde omgeving en voorbereidingen te treffen voor
productie-implementatie met Invato API-integratie.

## Inhoudsopgave

- [Vereisten](#vereisten)
- [Installatieproces](#installatieproces)
- [Ontwikkelproces](#ontwikkelproces)

## Vereisten

Voordat je begint, zorg ervoor dat je 1 van de volgende develop straten hebt geïnstalleerd en geconfigureerd:

- **Laravel Herd Pro**: Herd is a blazing fast, native Laravel and PHP development environment for macOS. Download het
  van de [officiële Laravel Herd-website](https://herd.laravel.com/).
- **Docker Desktop**: Essentieel voor het uitvoeren van gecontaineriseerde applicaties. Download het van
  de [officiële Docker-website](https://www.docker.com/products/docker-desktop).

    - Gebruik de Invato Traefik-afbeelding om netwerkroutering voor ons project in te stellen. Je kunt deze vinden in de
      Invato Bitbucket-repository. Voor meer details,
      bezoek [Invato Traefik Proxy](https://bitbucket.org/invato/traefik-proxy/src/master/).

## Installatieproces Laravel Herd Pro

Volg deze stappen om de projectomgeving op te zetten:

1. **Voer het installatiecommando uit**: Dit installeert vendor-bestanden, controleert bestandsrechten en initieert Herd
   Pro.

   ```shell
   sh ./herd-install.sh
   ```

2. **Toegang tot het project**: Na de installatie is het project toegankelijk
   op `https://{env:APP_NAME}.test`. Zorg ervoor dat je `{env:APP_NAME}` vervangt door de
   daadwerkelijke waarde van de omgevingsvariabele..

## Installatieproces Docker

Volg deze stappen om de projectomgeving op te zetten:

1. **Voer het installatiecommando uit**: Dit installeert vendor-bestanden, controleert bestandsrechten en start de
   Docker-container.

   ```shell
   sh ./docker-install-container.sh
   ```

2. **Toegang tot het project**: Na de installatie is het project toegankelijk
   op `https://{env:TRAEFIK_HOST}.docker.localhost`. Zorg ervoor dat je `{env:TRAEFIK_HOST}` vervangt door de
   daadwerkelijke waarde van de omgevingsvariabele..

## Ontwikkelproces

In de package.json zijn verschillende manieren beschikbaar voor ontwikkeling en productie.
Elk commando heeft op basis van de gekozen develop straat mogelijk een prefix nodig, in het geval van Laravel Herd Pro
geen
prefix, en in het geval van Docker heb je het prefix '**sail**' nodig
Hier volgt een uitleg van de verschillende commando's:

- **install**: Hiermee installeer je de dependencies voor het thema **ocms-basis**.
    ```
    npm run install
    ```

- **dev** en **build**: Deze commando's zijn bedoeld voor het opzetten van de CSS voor het **ocms-basis** thema.
    - **dev**: Start de ontwikkelmodus voor het CSS van **ocms-basis**.
      ```
      npm run dev
      ```
    - **build**: Bouwt de productieversie van het CSS voor **ocms-basis**.
      ```
      npm run build
      ```

- **dev-child** en **build-child**: Deze commando's zijn bedoeld voor het opzetten van de CSS voor het *
  *ocms-basis-child** thema.
    - **dev-child**: Start de ontwikkelmodus voor het CSS van **ocms-basis-child**.
      ```
      npm run dev-child
      ```
    - **build-child**: Bouwt de productieversie van het CSS voor **ocms-basis-child**.
      ```
      npm run build-child
      ```
- De overige commando's zijn bedoeld voor de JavaScript-bestanden van **ocms-basis**.
    - **development**: Voert de mix-taken uit voor JavaScript in ontwikkelmodus.
        ```
        npm run mix
        ```
    - **watch**: Houdt de bestanden in de gaten en compileert ze opnieuw bij wijzigingen.
        ```
        npm run watch
        ```
    - **watch-poll**: Zoals watch, maar controleert periodiek op wijzigingen.
      ```
      npm run watch-poll
      ```
    - **hot**: Start de hot-reload modus voor snellere ontwikkelcycli.
      ```
      npm run hot
      ```
    - **production**: Bouwt de productieversie van de JavaScript-bestanden.
      ```
      npm run production
      ```
