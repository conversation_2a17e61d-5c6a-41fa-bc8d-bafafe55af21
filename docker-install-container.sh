cleanup() {
    echo ""
    echo "Stopping installation"
    exit 1
}

trap cleanup INT

# styling variables
RED='\033[0;31m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

docker info > /dev/null 2>&1

# Ensure that <PERSON><PERSON> is running...
if [ $? -ne 0 ]; then
    echo "Docker is not running."

    exit 1
fi

# copy .env.example to .env
if [ ! -f .env ]; then
    cp .env.example .env
fi

# wait for filesystem
sleep 0.5

# set .env variable based on current project
# get current directory
appCurrentDirectory=$(basename $(pwd))

# app url
appHost="${appCurrentDirectory}.docker.localhost"

# find best port but higher than 3306
appCurrentPort=3307
while true; do
    if ! nc -z 127.0.0.1 $appCurrentPort; then
        break
    fi
    appCurrentPort=$((appCurrentPort + 1))
done

set_env_variable() {
    key=$1
    value=$2

    sed -i '' -e "s|${key}=.*|${key}=${value}|" .env
    echo "${RED}${key}${NC} set to: ${CYAN}${value}${NC}"
    sleep 0.2
}

# set all .env variables
set_env_variable "APP_NAME" "${appCurrentDirectory}"
set_env_variable "APP_URL" "https://${appHost}"
set_env_variable "TRAEFIK_HOST" "${appCurrentDirectory}"
set_env_variable "DB_DATABASE" "${appCurrentDirectory}"
set_env_variable "FORWARD_DB_PORT" "${appCurrentPort}"
set_env_variable "MAIL_MAILER" "smtp"
set_env_variable "MAIL_HOST" "host.docker.internal"
set_env_variable "MAIL_PORT" "2525"
set_env_variable "MAIL_USERNAME" "${appCurrentDirectory}"
set_env_variable "MAIL_PASSWORD" "null"
set_env_variable "MAIL_ENCRYPTION" "null"
set_env_variable "MAIL_FROM_NAME" "${appCurrentDirectory}"
set_env_variable "MAIL_FROM_ADDRESS" "ocms@${appHost}"
set_env_variable "LINK_POLICY" "secure"

# start a simple docker container to get Composer en PHP working en pull Vendor files
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v $(pwd):/var/www/html \
    -v ~/.composer/auth.json:/.composer/auth.json:delegated \
    -w /var/www/html \
    laravelsail/php83-composer:latest \
    composer install --ignore-platform-reqs --no-cache

# build the container images
./vendor/bin/sail build --no-cache

# set correct rights
if sudo -n true 2>/dev/null; then
    sudo chown -R $USER: .
    echo -e "${BOLD}Container will now be started${NC}"
else
    echo -e "${BOLD}Please provide your password so we can make some final adjustments to your application's permissions.${NC}"
    echo ""
    sudo chown -R $USER: .
    echo ""
    echo -e "${BOLD}Thank you! We hope you build something incredible. Container will now be started${NC}"
fi

# Start container
./vendor/bin/sail up -d

# wait for mysql to be started until migration can start. check once per second for 5 seconds
wait_for_mysql() {
    local max_attempts=10
    local attempt=1
    local mysql_host="127.0.0.1"
    local mysql_port="${appCurrentPort}"

    echo "${CYAN}Waiting on MySQL-container...${NC}"

    until nc -z "${mysql_host}" "${mysql_port}" 2>/dev/null || [ $attempt -eq $max_attempts ]; do
        sleep 1
        attempt=$((attempt + 1))
    done

    if [ $attempt -eq $max_attempts ]; then
        echo "${RED}Timeout reached. MySQL container may not have started completely.${NC}"
        exit 1
    fi
}

# wait for mysql connection to work
wait_for_mysql

sleep 10

# via sub query to have a working sail->mysql connection
(
    ./vendor/bin/sail composer install
    ./vendor/bin/sail artisan key:generate
    ./vendor/bin/sail artisan october:migrate
)


url="https://${appCurrentDirectory}.docker.localhost"
mysql="mysql://sail:password@127.0.0.1:${appCurrentPort}/${appCurrentDirectory}?statusColor=007F3D&env=local&name=localhost%20${appCurrentDirectory}&tLSMode=0&usePrivateKey=false&safeModeLevel=0&advancedSafeModeLevel=0&driverVersion=0&lazyload=false"

# show correct info:
echo "+---------------+------------------+"
echo "| ${RED}URL${NC}         | ${CYAN}${url}${NC}"
echo "| ${RED}Backend URL${NC} | ${CYAN}${url}/backend${NC}"
echo "| ${RED}TablePlus${NC}   | ${CYAN}${mysql}${NC}"
echo "| ${RED}MYSQL port${NC}  | ${CYAN}${appCurrentPort}${NC}"
echo "+---------------+------------------+"
