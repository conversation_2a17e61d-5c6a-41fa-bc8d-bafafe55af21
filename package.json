{"name": "ocms-basis", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"install": "npm install --prefix ./themes/ocms-basis", "update": "npm install --prefix ./themes/ocms-basis", "dev": "npm --prefix ./themes/ocms-basis run dev", "build": "npm --prefix ./themes/ocms-basis run build", "dev-child": "npm --prefix ./themes/ocms-basis run dev-child", "build-child": "npm --prefix ./themes/ocms-basis run build-child", "development": "npm --prefix ./themes/ocms-basis run development", "watch": "npm --prefix ./themes/ocms-basis run watch", "watch-poll": "npm --prefix ./themes/ocms-basis run watch-poll", "hot": "npm --prefix ./themes/ocms-basis run hot", "production": "npm --prefix ./themes/ocms-basis run production"}}