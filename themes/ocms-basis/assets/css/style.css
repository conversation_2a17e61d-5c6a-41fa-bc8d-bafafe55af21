*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*
! tailwindcss v3.4.13 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: Inter, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='checkbox']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {
  [type='radio']:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {
  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}

body {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

/* Hide scrollbar on Chromium */

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* For IE, Edge and Firefox */

.scrollbar-hide {
  /* IE and Edge */
  -ms-overflow-style: none;
  /* Firefox */
  scrollbar-width: none;
}

h1, .h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 700;
  line-height: 1.375;
  letter-spacing: -0.025em;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  h1, .h1 {
    font-size: 3rem;
    line-height: 1.375;
  }
}

@media (min-width: 1024px) {
  h1, .h1 {
    font-size: 3.75rem;
    line-height: 1.375;
  }
}

@media (min-width: 1280px) {
  h1, .h1 {
    line-height: 1.375;
  }
}

h2, .h2 {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  line-height: 1.375;
  letter-spacing: -0.025em;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  h2, .h2 {
    font-size: 1.875rem;
    line-height: 2.25rem;
    line-height: 1.375;
  }
}

@media (min-width: 1024px) {
  h2, .h2 {
    font-size: 2.25rem;
    line-height: 2.5rem;
    line-height: 1.375;
  }
}

@media (min-width: 1280px) {
  h2, .h2 {
    line-height: 1.375;
  }
}

h3, .h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
  line-height: 1.375;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  h3, .h3 {
    line-height: 1.375;
  }
}

@media (min-width: 1024px) {
  h3, .h3 {
    font-size: 1.5rem;
    line-height: 2rem;
    line-height: 1.375;
  }
}

@media (min-width: 1280px) {
  h3, .h3 {
    line-height: 1.375;
  }
}

h4, .h4 {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

h5, .h5 {
  margin-bottom: 0.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

h5:where(.dark, .dark *), .h5:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

h1 strong,
h2 strong,
h3 strong {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

h1.lead {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 300;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

@media (min-width: 640px) {
  h1.lead {
    font-size: 3rem;
    line-height: 1;
  }
}

@media (min-width: 1024px) {
  h1.lead {
    font-size: 3.75rem;
    line-height: 1;
  }
}

@media (min-width: 1536px) {
  h1.lead {
    font-size: 4.5rem;
    line-height: 1;
  }
}

a {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.content_section h1 {
  margin-bottom: 1rem;
}

.content_section h2 {
  margin-bottom: 0.75rem;
}

.content_section h3 {
  margin-bottom: 0.5rem;
}

.content_section h1 a,
.content_section h2 a,
.content_section h3 a {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.content_section h1 a:hover,
.content_section h2 a:hover,
.content_section h3 a:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
  text-decoration-line: underline;
}

.content_section h1 b,
.content_section h1 strong,
.content_section h2 b,
.content_section h2 strong,
.content_section h3 b,
.content_section h3 strong {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.content_section h4 {
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .content_section h4 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.content_section h1.success_heading,
.content_section h2.success_heading,
.content_section h3.success_heading,
.content_section h4.success_heading {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}

.content_section p {
  margin-bottom: 1.5rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.content_section p a:not(.btn):not(.btn-lg) {
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.content_section p a:not(.btn):not(.btn-lg):hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
  text-decoration-line: underline;
}

.content_section img {
  max-width: 100%;
}

.content_section ul, .content_section ol {
  margin-bottom: 1rem;
}

.content_section ul > :not([hidden]) ~ :not([hidden]), .content_section ol > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.content_section ul, .content_section ol {
  padding-left: 1rem;
}

.content_section ul {
  list-style-type: disc;
}

.content_section ul *::marker {
  color: rgb(234 179 8 );
}

.content_section ul::marker {
  color: rgb(234 179 8 );
}

.content_section ol {
  list-style-type: decimal;
}

.content_section li {
  font-size: 1rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.content_section table {
  width: 100%;
  vertical-align: top;
}

.content_section table tr {
  vertical-align: top;
}

.content_section table td, .content_section table th {
  vertical-align: top;
}

.content_section blockquote {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-left-width: 4px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
  padding: 1rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 500;
  font-style: italic;
  line-height: 1.625;
}

.content_section blockquote p {
  margin: 0px;
}

.content_section blockquote p::before {
  --tw-content: '“';
  content: var(--tw-content);
}

.content_section blockquote p::after {
  --tw-content: '„';
  content: var(--tw-content);
}

.content_section blockquote blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.content_section_sm p {
  margin-bottom: 1rem;
  font-size: 0.875rem;
  line-height: 1.5rem;
}

.content_section_sm li {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.content_section_sm h2 {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
  letter-spacing: -0.025em;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .content_section_sm h2 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

@media (min-width: 1024px) {
  .content_section_sm h2 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

.content_section_faded p,
.light .content_section_faded p {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.dark .content_section h2,
.dark .content_section h3 {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .content_section p {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity));
}

.dark .content_section h4 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.dark .content_section blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark .content_section blockquote blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.dark .content_section_faded p {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.light .content_section h2 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.light .content_section h3 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.light .content_section p {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.light .content_section h4 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.light .content_section blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.light .content_section blockquote blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.content_section.p-lg p {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.content_section.p-xl p {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

div:has(.prose) :is(:where(h1, h2, h3, h4, h5, h6, th):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  text-wrap: balance;
  line-height: 1.375;
}

div:has(.prose) :is(:where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

div:has(.prose) :is(:where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-size: 1.5rem;
  line-height: 2rem;
}

div:has(.prose) :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

div:has(.prose) :is(:where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
}

div:has(.prose) :is(:where(img):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  margin-top: 0px;
  margin-bottom: 0px;
}

div:has(.prose) :is(:where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-weight: 300;
}

@media (min-width: 768px) {
  div:has(.prose) :is(:where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
    font-size: 3rem;
    line-height: 1;
  }

  div:has(.prose) :is(:where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  div:has(.prose) :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  div:has(.prose) :is(:where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  div:has(.prose) :is(:where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
    font-size: 3.75rem;
    line-height: 1;
  }

  div:has(.prose) :is(:where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.sidebar-item div:has(.prose) :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

@media (min-width: 768px) {
  .sidebar-item div:has(.prose) :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.secondary-title-color div:has(.prose) :is(:where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.secondary-title-color div:has(.prose) :is(:where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.secondary-title-color div:has(.prose) :is(:where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.secondary-title-color div:has(.prose) :is(:where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.fr-img-caption .fr-inner {
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.fr-img-caption .fr-img-wrap img {
  margin-bottom: 0px;
}

img.fr-dii.fr-fil,
span.fr-img-caption.fr-dii.fr-fil {
  margin-right: 1rem;
}

img.fr-dii.fr-fir,
span.fr-img-caption.fr-dii.fr-fir {
  margin-left: 1rem;
}

img.fr-dib,
span.fr-img-caption.fr-dib {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;
}

@media (min-width: 425px) {
  .container {
    max-width: 425px;
  }
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.aspect-h-3 {
  --tw-aspect-h: 3;
}

.aspect-w-4 {
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 4;
}

.aspect-w-4 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.prose {
  color: var(--tw-prose-body);
  max-width: 65ch;
}

.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}

.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)):hover {
  color: #2563eb;
}

.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}

.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: decimal;
}

.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-inline-start: 1.625em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}

.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.25em;
}

.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}

.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-inline-start-width: 0.25rem;
  border-inline-start-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1em;
}

.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: open-quote;
}

.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: close-quote;
}

.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 1rem;
  line-height: 1.1111111;
}

.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}

.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 0.75rem;
  line-height: 1.3333333;
}

.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}

.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  display: block;
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 500;
  font-family: inherit;
  color: var(--tw-prose-kbd);
  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
  font-size: 0.875em;
  border-radius: 0.3125rem;
  padding-top: 0.1875em;
  padding-inline-end: 0.375em;
  padding-bottom: 0.1875em;
  padding-inline-start: 0.375em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: "`";
}

.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: "`";
}

.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}

.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}

.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-inline-end: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-inline-start: 1.1428571em;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
  content: none;
}

.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
  content: none;
}

.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}

.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}

.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}

.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-bottom-width: 0;
}

.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: baseline;
}

.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}

.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  vertical-align: top;
}

.prose :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  text-align: start;
}

.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}

.prose {
  --tw-prose-body: #374151;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-kbd: #111827;
  --tw-prose-kbd-shadows: 17 24 39;
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-kbd: #fff;
  --tw-prose-invert-kbd-shadows: 255 255 255;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: 1rem;
  line-height: 1.75;
}

.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.375em;
}

.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5em;
  padding-inline-start: 1.625em;
}

.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-inline-end: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-inline-start: 0.5714286em;
}

.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose :where(h1 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
}

.prose :where(h2 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 800;
}

.prose :where(h3 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
}

.prose :where(h4 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-weight: 700;
}

.prose-sm {
  font-size: 0.875rem;
  line-height: 1.7142857;
}

.prose-sm :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}

.prose-sm :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  line-height: 1.5555556;
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}

.prose-sm :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.3333333em;
  margin-bottom: 1.3333333em;
  padding-inline-start: 1.1111111em;
}

.prose-sm :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.1428571em;
  margin-top: 0;
  margin-bottom: 0.8em;
  line-height: 1.2;
}

.prose-sm :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.4285714em;
  margin-top: 1.6em;
  margin-bottom: 0.8em;
  line-height: 1.4;
}

.prose-sm :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2857143em;
  margin-top: 1.5555556em;
  margin-bottom: 0.4444444em;
  line-height: 1.5555556;
}

.prose-sm :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.4285714em;
  margin-bottom: 0.5714286em;
  line-height: 1.4285714;
}

.prose-sm :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-sm :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  border-radius: 0.3125rem;
  padding-top: 0.1428571em;
  padding-inline-end: 0.3571429em;
  padding-bottom: 0.1428571em;
  padding-inline-start: 0.3571429em;
}

.prose-sm :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
}

.prose-sm :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-sm :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8888889em;
}

.prose-sm :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.6666667;
  margin-top: 1.6666667em;
  margin-bottom: 1.6666667em;
  border-radius: 0.25rem;
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  margin-bottom: 0.2857143em;
}

.prose-sm :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}

.prose-sm :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4285714em;
}

.prose-sm :where(.prose-sm > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}

.prose-sm :where(.prose-sm > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose-sm > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(.prose-sm > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose-sm > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}

.prose-sm :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
  margin-bottom: 1.1428571em;
}

.prose-sm :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.2857143em;
  padding-inline-start: 1.5714286em;
}

.prose-sm :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2.8571429em;
  margin-bottom: 2.8571429em;
}

.prose-sm :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.5;
}

.prose-sm :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-sm :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-sm :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.6666667em;
  padding-inline-end: 1em;
  padding-bottom: 0.6666667em;
  padding-inline-start: 1em;
}

.prose-sm :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-sm :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-sm :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
}

.prose-sm :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-sm :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8571429em;
  line-height: 1.3333333;
  margin-top: 0.6666667em;
}

.prose-sm :where(.prose-sm > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(.prose-sm > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-xl {
  font-size: 1.25rem;
  line-height: 1.8;
}

.prose-xl :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose-xl :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.2em;
  line-height: 1.5;
  margin-top: 1em;
  margin-bottom: 1em;
}

.prose-xl :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-inline-start: 1.0666667em;
}

.prose-xl :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 2.8em;
  margin-top: 0;
  margin-bottom: 0.8571429em;
  line-height: 1;
}

.prose-xl :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.8em;
  margin-top: 1.5555556em;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose-xl :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 1.5em;
  margin-top: 1.6em;
  margin-bottom: 0.6666667em;
  line-height: 1.3333333;
}

.prose-xl :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.8em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose-xl :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-xl :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  border-radius: 0.3125rem;
  padding-top: 0.25em;
  padding-inline-end: 0.4em;
  padding-bottom: 0.25em;
  padding-inline-start: 0.4em;
}

.prose-xl :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-xl :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.8611111em;
}

.prose-xl :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
}

.prose-xl :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  line-height: 1.7777778;
  margin-top: 2em;
  margin-bottom: 2em;
  border-radius: 0.5rem;
  padding-top: 1.1111111em;
  padding-inline-end: 1.3333333em;
  padding-bottom: 1.1111111em;
  padding-inline-start: 1.3333333em;
}

.prose-xl :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
  padding-inline-start: 1.6em;
}

.prose-xl :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
  padding-inline-start: 1.6em;
}

.prose-xl :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6em;
  margin-bottom: 0.6em;
}

.prose-xl :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4em;
}

.prose-xl :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0.4em;
}

.prose-xl :where(.prose-xl > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}

.prose-xl :where(.prose-xl > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(.prose-xl > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.2em;
}

.prose-xl :where(.prose-xl > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(.prose-xl > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 1.2em;
}

.prose-xl :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}

.prose-xl :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose-xl :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0.6em;
  padding-inline-start: 1.6em;
}

.prose-xl :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2.8em;
  margin-bottom: 2.8em;
}

.prose-xl :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  line-height: 1.5555556;
}

.prose-xl :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0.6666667em;
  padding-bottom: 0.8888889em;
  padding-inline-start: 0.6666667em;
}

.prose-xl :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-xl :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-xl :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-top: 0.8888889em;
  padding-inline-end: 0.6666667em;
  padding-bottom: 0.8888889em;
  padding-inline-start: 0.6666667em;
}

.prose-xl :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-start: 0;
}

.prose-xl :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  padding-inline-end: 0;
}

.prose-xl :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose-xl :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose-xl :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  font-size: 0.9em;
  line-height: 1.5555556;
  margin-top: 1em;
}

.prose-xl :where(.prose-xl > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(.prose-xl > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-sky {
  --tw-prose-links: #0284c7;
  --tw-prose-invert-links: #0ea5e9;
}

.prose-primary {
  --tw-prose-body: #4b5563;
  --tw-prose-headings: #4b5563;
  --tw-prose-lead: #374151;
  --tw-prose-links: #3b82f6;
  --tw-prose-bold: #374151;
  --tw-prose-counters: #2563eb;
  --tw-prose-bullets: #3b82f6;
  --tw-prose-hr: #d1d5db;
  --tw-prose-quotes: #374151;
  --tw-prose-quote-borders: #60a5fa;
  --tw-prose-captions: #9ca3af;
  --tw-prose-code: #1e3a8a;
  --tw-prose-pre-code: #f3f4f6;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
}

.prose-primary :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.prose-primary :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.prose-primary :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.prose-primary :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #1d4ed8;
}

.prose-primary :where(h1 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.prose-primary :where(h2 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.prose-primary :where(h3 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.prose-primary :where(h4 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.prose-primary :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #374151;
}

.prose-primary :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #374151;
}

.prose-primary :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #4b5563;
}

.prose-primary :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.prose-primary :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)):hover {
  color: #2563eb;
}

.btn-primary.btn-filled {
  background-color: #2563eb;
  color: #fff;
}

.btn-primary.btn-filled:hover {
  background-color: #3b82f6;
}

.btn-primary.btn-filled:focus-visible {
  outline-color: #2563eb;
}

.btn-primary.btn-outlined {
  color: #2563eb;
  border-width: 1px;
  border-color: #2563eb;
}

.btn-primary.btn-outlined:hover {
  background-color: #2563eb;
  color: #fff;
}

.btn-primary.btn-tonal {
  background-color: #bfdbfe;
  color: #1d4ed8;
}

.btn-primary.btn-tonal:hover {
  background-color: #93c5fd;
  color: #1d4ed8;
}

.btn-primary.btn-tonal:focus-visible {
  outline-color: #93c5fd;
}

.btn-primary.btn-link {
  background-color: transparent;
  color: #3b82f6;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-primary.btn-link:hover {
  background-color: transparent;
  color: #2563eb;
}

.btn-primary.btn-link:focus-visible {
  outline-color: #93c5fd;
}

.btn-secondary.btn-filled {
  background-color: #eab308;
  color: #fff;
}

.btn-secondary.btn-filled:hover {
  background-color: #ca8a04;
}

.btn-secondary.btn-filled:focus-visible {
  outline-color: #eab308;
}

.btn-secondary.btn-outlined {
  color: #eab308;
  border-width: 1px;
  border-color: #eab308;
}

.btn-secondary.btn-outlined:hover {
  background-color: #eab308;
  color: #fff;
}

.btn-secondary.btn-tonal {
  background-color: #fef08a;
  color: #a16207;
}

.btn-secondary.btn-tonal:hover {
  background-color: #fde047;
  color: #a16207;
}

.btn-secondary.btn-tonal:focus-visible {
  outline-color: #fde047;
}

.btn-secondary.btn-link {
  background-color: transparent;
  color: #eab308;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-secondary.btn-link:hover {
  background-color: transparent;
  color: #ca8a04;
}

.btn-secondary.btn-link:focus-visible {
  outline-color: #fde047;
}

.btn-grayscale.btn-filled {
  background-color: #6b7280;
  color: #fff;
}

.btn-grayscale.btn-filled:hover {
  background-color: #4b5563;
}

.btn-grayscale.btn-filled:focus-visible {
  outline-color: #6b7280;
}

.btn-grayscale.btn-outlined {
  color: #6b7280;
  border-width: 1px;
  border-color: #6b7280;
}

.btn-grayscale.btn-outlined:hover {
  background-color: #6b7280;
  color: #fff;
}

.btn-grayscale.btn-tonal {
  background-color: #e5e7eb;
  color: #374151;
}

.btn-grayscale.btn-tonal:hover {
  background-color: #d1d5db;
  color: #374151;
}

.btn-grayscale.btn-tonal:focus-visible {
  outline-color: #d1d5db;
}

.btn-grayscale.btn-link {
  background-color: transparent;
  color: #6b7280;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-grayscale.btn-link:hover {
  background-color: transparent;
  color: #4b5563;
}

.btn-grayscale.btn-link:focus-visible {
  outline-color: #d1d5db;
}

.btn-error.btn-filled {
  background-color: #ef4444;
  color: #fff;
}

.btn-error.btn-filled:hover {
  background-color: #dc2626;
}

.btn-error.btn-filled:focus-visible {
  outline-color: #ef4444;
}

.btn-error.btn-outlined {
  color: #dc2626;
  border-width: 1px;
  border-color: #dc2626;
}

.btn-error.btn-outlined:hover {
  background-color: #dc2626;
  color: #fff;
}

.btn-error.btn-tonal {
  background-color: #fecaca;
  color: #b91c1c;
}

.btn-error.btn-tonal:hover {
  background-color: #fca5a5;
  color: #b91c1c;
}

.btn-error.btn-tonal:focus-visible {
  outline-color: #fca5a5;
}

.btn-error.btn-link {
  background-color: transparent;
  color: #ef4444;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-error.btn-link:hover {
  background-color: transparent;
  color: #dc2626;
}

.btn-error.btn-link:focus-visible {
  outline-color: #fca5a5;
}

.btn-info.btn-filled {
  background-color: #06b6d4;
  color: #fff;
}

.btn-info.btn-filled:hover {
  background-color: #0891b2;
}

.btn-info.btn-filled:focus-visible {
  outline-color: #06b6d4;
}

.btn-info.btn-outlined {
  color: #0891b2;
  border-width: 1px;
  border-color: #0891b2;
}

.btn-info.btn-outlined:hover {
  background-color: #0891b2;
  color: #fff;
}

.btn-info.btn-tonal {
  background-color: #a5f3fc;
  color: #0e7490;
}

.btn-info.btn-tonal:hover {
  background-color: #67e8f9;
  color: #0e7490;
}

.btn-info.btn-tonal:focus-visible {
  outline-color: #67e8f9;
}

.btn-info.btn-link {
  background-color: transparent;
  color: #06b6d4;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-info.btn-link:hover {
  background-color: transparent;
  color: #0891b2;
}

.btn-info.btn-link:focus-visible {
  outline-color: #67e8f9;
}

.btn-white.btn-filled {
  color: #3b82f6;
  background-color: #fff;
}

.btn-white.btn-filled:hover {
  color: #2563eb;
}

.btn-white.btn-filled:focus-visible {
  outline-color: #fff;
}

.btn-white.btn-outlined {
  color: #fff;
  border-width: 1px;
  border-color: #fff;
}

.btn-white.btn-outlined:hover {
  background-color: #fff;
  color: #2563eb;
}

.btn-white.btn-tonal {
  background-color: rgba(255,255,255,.7);
  color: #1d4ed8;
}

.btn-white.btn-tonal:hover {
  background-color: #fff;
  color: #1d4ed8;
}

.btn-white.btn-tonal:focus-visible {
  outline-color: rgba(255,255,255,.4);
}

.btn-white.btn-link {
  background-color: transparent;
  color: #fff;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-white.btn-link:hover {
  background-color: transparent;
  color: #f3f4f6;
}

.btn-white.btn-link:focus-visible {
  outline-color: #f3f4f6;
}

.btn-success.btn-filled {
  background-color: #16a34a;
  color: #fff;
}

.btn-success.btn-filled:hover {
  background-color: #22c55e;
}

.btn-success.btn-filled:focus-visible {
  outline-color: #16a34a;
}

.btn-success.btn-outlined {
  color: #16a34a;
  border-width: 1px;
  border-color: #16a34a;
}

.btn-success.btn-outlined:hover {
  background-color: #16a34a;
  color: #fff;
}

.btn-success.btn-tonal {
  background-color: #bbf7d0;
  color: #15803d;
}

.btn-success.btn-tonal:hover {
  background-color: #86efac;
  color: #15803d;
}

.btn-success.btn-tonal:focus-visible {
  outline-color: #86efac;
}

.btn-success.btn-link {
  background-color: transparent;
  color: #22c55e;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
}

.btn-success.btn-link:hover {
  background-color: transparent;
  color: #16a34a;
}

.btn-success.btn-link:focus-visible {
  outline-color: #86efac;
}

.alert {
  position: static;
  margin-top: 0.5rem;
  margin-bottom: 2rem;
  padding: 0.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.alert-info {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
}

.alert-success {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.alert-warning {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}

.alert-danger, .alert-error {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

[data-name="blog-detail"] .additional-content-cta h2 {
  margin-top: 0px;
}

.blog-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 100ms;
}

.blog-item .blog-item-thumb {
  position: relative;
  aspect-ratio: 4 / 3;
  overflow: hidden;
  border-width: 1px;
  border-bottom-width: 0px;
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}

.blog-item .blog-item-categories {
  position: absolute;
  z-index: 30;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.5rem;
}

.blog-item .blog-item-category {
  display: inline-block;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 700;
  line-height: 1;
  letter-spacing: 0.025em;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.blog-item .blog-item-image {
  height: 100%;
  width: 100%;
  border-radius: 0.25rem;
  -o-object-fit: cover;
     object-fit: cover;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.blog-item .blog-item-info {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.blog-item .blog-item-info-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.blog-item .blog-item-title h3 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.blog-item .blog-item-excerpt {
  flex: 1 1 auto;
}

.blog-item .blog-item-excerpt.content_section p {
  margin: 0px;
}

.blog-item .blog-item-button {
  margin-top: auto;
}

.blog-card {
  position: relative;
  height: 100%;
  gap: 0px;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.blog-card-body {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  gap: 0.5rem;
  border-bottom-right-radius: 0.125rem;
  border-bottom-left-radius: 0.125rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

.blog-item .blog-item-thumb {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.blog-card .blog-item-image {
  height: 100%;
  width: 100%;
  border-top-left-radius: 0.125rem;
  border-top-right-radius: 0.125rem;
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
  -o-object-fit: cover;
     object-fit: cover;
}

.blog-card .blog-item-title h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.blog-card .blog-item-overlay-link {
  position: absolute;
  inset: 0px;
}

/* Hover */

.blog-card:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: rgb(0 0 0 / 0.05);
  --tw-shadow: var(--tw-shadow-colored);
}

.blog-card:hover .blog-item-image {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Postlist 3 */

#postlist-3 .blog-item-2 {
  grid-column: span 2 / span 2;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}

#postlist-3 .blog-item-2 .blog-item-thumb {
  position: static;
  padding-bottom: 0;
}

#postlist-3 .blog-item-2 .blog-item-thumb > * {
  position: static;
  height: auto;
  width: auto;
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
}

#postlist-3 .blog-item-2 .blog-item-thumb {
  position: relative;
  height: 100%;
  width: 100%;
  border-radius: 0.25rem;
}

#postlist-3 .blog-item-2 .blog-item-categories {
  position: absolute;
}

#postlist-3 .blog-item-2 .blog-card-body {
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 50%;
  border-radius: 0px;
  border-top-right-radius: 0.25rem;
  border-width: 0px;
  border-top-width: 1px;
  border-right-width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.blog-item-card {
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.blog-item-card:where(.dark, .dark *) {
  background-color: rgb(255 255 255 / 0.1);
}

.blog-item-card .blog-item-content {
  padding: 1rem;
}

.blog-item-card .featured.blog-item-content {
  padding: 2rem;
}

.blog-item-card .blog-item-content .blog-item-info {
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.button_wrapper {
  margin-top: 2.5rem;
  align-items: center;
}

.button_wrapper > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.btn-sm {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
}

@media (min-width: 768px) {
  .btn-sm {
    display: inline-flex;
    width: auto;
    justify-content: flex-start;
  }
}

.btn {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-weight: 600;
}

@media (min-width: 768px) {
  .btn {
    display: inline-flex;
    width: auto;
    justify-content: flex-start;
  }
}

.btn-lg {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-left: 3rem;
  padding-right: 3rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
}

@media (min-width: 768px) {
  .btn-lg {
    display: inline-flex;
    width: auto;
    justify-content: flex-start;
  }
}

.btn-sm:focus-visible, .btn:focus-visible, .btn-lg:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
}

header[data-section="navbar"] .btn {
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

@media (min-width: 768px) {
  header[data-section="navbar"] .btn {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1280px) {
  header[data-section="navbar"] .btn {
    padding-left: 2rem;
    padding-right: 2rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.btn-rounded {
  border-radius: 0.375rem;
}

.btn-sm.btn-rounded {
  border-radius: 0.25rem;
}

.btn-pill {
  border-radius: 999px;
}

.btn-elevated {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-filled.btn-primary,
.btn-elevated.btn-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-primary:hover,
.btn-elevated.btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.btn-filled.btn-primary:focus-visible,
.btn-elevated.btn-primary:focus-visible {
  outline-color: #2563eb;
}

.btn-tonal.btn-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}

.btn-tonal.btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity));
}

.btn-tonal.btn-primary:focus-visible {
  outline-color: #93c5fd;
}

.btn-outlined.btn-primary {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.btn-outlined.btn-primary:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-primary {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.btn-filled.btn-secondary,
.btn-elevated.btn-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-secondary:hover,
.btn-elevated.btn-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity));
}

.btn-filled.btn-secondary:focus-visible,
.btn-elevated.btn-secondary:focus-visible {
  outline-color: #ca8a04;
}

.btn-tonal.btn-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity));
}

.btn-tonal.btn-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity));
}

.btn-tonal.btn-secondary:focus-visible {
  outline-color: #fde047;
}

.btn-outlined.btn-secondary {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.btn-outlined.btn-secondary:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-secondary {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-secondary:hover {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity));
}

.btn-filled.btn-grayscale,
.btn-elevated.btn-grayscale {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-grayscale:hover,
.btn-elevated.btn-grayscale:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.btn-filled.btn-grayscale:focus-visible,
.btn-elevated.btn-grayscale:focus-visible {
  outline-color: #4b5563;
}

.btn-tonal.btn-grayscale {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.btn-tonal.btn-grayscale:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.btn-tonal.btn-grayscale:focus-visible {
  outline-color: #d1d5db;
}

.btn-outlined.btn-grayscale {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.btn-outlined.btn-grayscale:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-grayscale {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-grayscale:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.btn-filled.btn-error,
.btn-elevated.btn-error {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-error:hover,
.btn-elevated.btn-error:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}

.btn-filled.btn-error:focus-visible,
.btn-elevated.btn-error:focus-visible {
  outline-color: #dc2626;
}

.btn-tonal.btn-error {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity));
}

.btn-tonal.btn-error:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity));
}

.btn-tonal.btn-error:focus-visible {
  outline-color: #fca5a5;
}

.btn-outlined.btn-error {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.btn-outlined.btn-error:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-error {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-error:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.btn-filled.btn-warning,
.btn-elevated.btn-warning {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-warning:hover,
.btn-elevated.btn-warning:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity));
}

.btn-filled.btn-warning:focus-visible,
.btn-elevated.btn-warning:focus-visible {
  outline-color: #ea580c;
}

.btn-tonal.btn-warning {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity));
}

.btn-tonal.btn-warning:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 116 / var(--tw-bg-opacity));
}

.btn-tonal.btn-warning:focus-visible {
  outline-color: #fdba74;
}

.btn-outlined.btn-warning {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}

.btn-outlined.btn-warning:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-warning {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-warning:hover {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity));
}

.btn-filled.btn-info,
.btn-elevated.btn-info {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-filled.btn-info:hover,
.btn-elevated.btn-info:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(8 145 178 / var(--tw-bg-opacity));
}

.btn-filled.btn-info:focus-visible,
.btn-elevated.btn-info:focus-visible {
  outline-color: #0891b2;
}

.btn-tonal.btn-info {
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(14 116 144 / var(--tw-text-opacity));
}

.btn-tonal.btn-info:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(103 232 249 / var(--tw-bg-opacity));
}

.btn-tonal.btn-info:focus-visible {
  outline-color: #67e8f9;
}

.btn-outlined.btn-info {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(6 182 212 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity));
}

.btn-outlined.btn-info:hover {
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-link.btn-info {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-info:hover {
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity));
}

.btn-filled.btn-white,
.btn-elevated.btn-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.btn-filled.btn-white:hover,
.btn-elevated.btn-white:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.btn-filled.btn-white:focus-visible,
.btn-elevated.btn-white:focus-visible {
  outline-color: #2563eb;
}

.btn-tonal.btn-white {
  background-color: rgb(255 255 255 / 0.7);
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}

.btn-tonal.btn-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.btn-tonal.btn-white:focus-visible {
  outline-color: #93c5fd;
}

.btn-outlined.btn-white {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.btn-outlined.btn-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.btn-link.btn-white {
  border-radius: 0px;
  padding: 0px;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-link.btn-white:hover {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.btn-primary-default {
  display: inline-block;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-primary-default:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.btn-primary-default:focus-visible {
  outline-color: #2563eb;
}

.btn-secondary-default {
  display: inline-block;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-secondary-default:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity));
}

.btn-secondary-default:focus-visible {
  outline-color: #ca8a04;
}

.btn-white-default {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.btn-white-default:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.btn-white-default:focus-visible {
  outline-color: #2563eb;
}

.card {
  margin-bottom: 1rem;
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  row-gap: 1rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 2.5rem;
  padding-right: 2.5rem;
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .card {
    margin-bottom: 0px;
  }
}

.card .card-icon {
  position: relative;
  display: flex;
  aspect-ratio: 1 / 1;
  width: 33.333333%;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
  font-size: 2.25rem;
  line-height: 2.5rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.card .card-title {
  margin-bottom: 0px;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.card .card-text p {
  margin: 0px;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.card .card-buttons {
  margin-top: auto;
}

.card .card-icon-sm {
  position: relative;
  display: flex;
  aspect-ratio: 1 / 1;
  width: 25%;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }

  .container.container-xl {
    max-width: 1536px;
  }
}

.content .content-title h2 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .content .media-row > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }
}

@media (min-width: 1024px) {
  .content .media-row > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

.content .content-usps {
  margin-top: 2rem;
  -moz-column-gap: 2rem;
       column-gap: 2rem;
}

.content .content-usps .content-usp {
  margin-bottom: 1rem;
  display: flex;
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

@media (min-width: 1024px) {
  .content .content-usps .content-usp {
    margin-bottom: 0px;
    display: block;
    -moz-column-gap: 0px;
         column-gap: 0px;
  }
}

.content .content-usps .content-usp .usp-icon {
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 1024px) {
  .content .content-usps .content-usp .usp-icon {
    margin-bottom: 0.5rem;
  }
}

.content .content-usps .content-usp .usp-title h4 {
  margin-top: 0px;
  margin-bottom: 0px;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.content .content-usps .content-usp .usp-text {
  grid-column: span 3 / span 3;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

#content-1 .content-title {
  margin-bottom: 2rem;
}

@media (min-width: 640px) {
  #content-1 .content-title {
    margin-bottom: 3rem;
  }
}

@media (min-width: 1024px) {
  #content-1 .content-title {
    margin-bottom: 4rem;
  }
}

#content-1 .content-title h2 {
  text-align: center;
}

#content-1 .card {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
  text-align: center;
}

#content-1 .content-row {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  max-width: 64rem;
  flex-direction: column;
  row-gap: 2rem;
  padding-left: 2rem;
  padding-right: 2rem;
}

@media (min-width: 1024px) {
  #content-1 .content-row {
    flex-direction: row;
    row-gap: 0px;
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }
}

#content-1 .content-row .card {
  flex: 1 1 0%;
}

#content-2.content {
  padding-top: 0px;
  padding-bottom: 0px;
}

@media (min-width: 768px) {
  #content-2.content {
    padding: 1rem;
  }
}

@media (min-width: 1024px) {
  #content-2.content {
    padding: 0px;
  }
}

#content-2 .card {
  max-width: 24rem;
  text-align: center;
}

#content-3 .row-grid {
  align-items: center;
  -moz-column-gap: 4rem;
       column-gap: 4rem;
}

@media (min-width: 640px) {
  #content-3 .row-grid {
    align-items: flex-start;
  }
}

@media (min-width: 1024px) {
  #content-3 .row-grid {
    align-items: center;
  }
}

/* #content-3.style-1 .row-grid,
#content-3.style-3 .row-grid { @apply gap-x-16; }
#content-3 .row-grid,
#content-3.style-2 .row-grid { @apply gap-x-16 items-center sm:items-start lg:items-center; }
#content-3.style-3 .content-grid-text { @apply py-16; } */

#cookies-bar {
  position: fixed;
  left: 0px;
  right: 0px;
  bottom: 0px;
  z-index: 50;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  box-shadow: 0 -20px 25px -5px rgba(0,0,0,.05), 0 -8px 10px -6px rgba(0,0,0,.05);
}

#cookies-bar .cookies-bar {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  align-items: center;
  row-gap: 2.5rem;
}

@media (min-width: 768px) {
  #cookies-bar .cookies-bar {
    flex-wrap: nowrap;
    gap: 2.5rem;
  }
}

#cookies-bar .cookiebar-icon {
  width: 25%;
}

@media (min-width: 768px) {
  #cookies-bar .cookiebar-icon {
    width: auto;
  }
}

#cookies-bar .cookiebar-content {
  width: 75%;
}

@media (min-width: 768px) {
  #cookies-bar .cookiebar-content {
    width: auto;
  }
}

#cookies-bar .title {
  margin-bottom: 0.25rem;
  font-weight: 700;
}

#cookies-bar .content p {
  margin-bottom: 0.75rem;
}

#cookies-bar .content p:last-of-type {
  margin-bottom: 0px;
}

#cookies-bar .content a {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

#cookies-bar .content a:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

#cookies-bar .buttons {
  position: relative;
  display: flex;
  width: 100%;
  flex-direction: column;
  row-gap: 0.5rem;
}

@media (min-width: 768px) {
  #cookies-bar .buttons {
    width: auto;
  }
}

#cookies-bar .decline-btn {
  position: absolute;
  top: 0px;
  right: 1rem;
  text-align: left;
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 768px) {
  #cookies-bar .decline-btn {
    right: auto;
    left: 100%;
    --tw-translate-x: 1rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}

.cookie-btn-primary {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-weight: 700;
  letter-spacing: 0.025em;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.cookie-btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

@media (min-width: 768px) {
  .cookie-btn-primary {
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }
}

.cookie-btn-default {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  border-radius: 0.375rem;
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-weight: 700;
  letter-spacing: 0.025em;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.cookie-btn-default:hover {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .cookie-btn-default {
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }
}

.cookie-btn-primary.hasDisableButton,
.cookie-btn-default.hasDisableButton {
  width: 83.333333%;
}

@media (min-width: 1024px) {
  .cookie-btn-primary.hasDisableButton,
.cookie-btn-default.hasDisableButton {
    width: 16rem;
  }
}

.cookie-btn-default svg {
  margin-left: 1rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

#cookies-bar .detailsbutton {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

#cookies-bar .detailsbutton:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.faq-wrapper h2 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.faq-header {
  margin-left: auto;
  margin-right: auto;
  max-width: 56rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
}

.faq-intro {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.faq-list {
  margin-left: auto;
  margin-right: auto;
  margin-top: 3rem;
  max-width: 64rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

.faq-item {
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 1024px) {
  .faq-item {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.faq-question {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  -moz-column-gap: 2rem;
       column-gap: 2rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  text-align: left;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

@media (min-width: 1024px) {
  .faq-question {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

.faq-answer-inner {
  padding-bottom: 1rem;
}

@media (min-width: 1024px) {
  .faq-answer-inner {
    padding-bottom: 1.5rem;
  }
}

[data-name="faq-1"] .faq-list > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

[data-name="faq-2"] .faq-list > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity));
}

[data-name="faq-2"] .faq-item {
  border-width: 0px;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

[data-name="faq-2"] .faq-question {
  padding-top: 0px;
  padding-bottom: 0px;
}

@media (min-width: 1024px) {
  [data-name="faq-2"] .faq-question {
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

[data-name="faq-3"] .faq-icon {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}

[data-name="faq-3"] .faq-icon:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

[data-name="faq-2"] .faq-answer-inner {
  padding-bottom: 0px;
  padding-top: 1.5rem;
  padding-right: 2rem;
}

@media (min-width: 1024px) {
  [data-name="faq-2"] .faq-answer-inner {
    padding-bottom: 0px;
    padding-right: 4rem;
  }
}

[data-name="faq-3"] .faq-list > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  --tw-divide-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-divide-opacity));
}

[data-name="faq-3"] .faq-item {
  border-width: 0px;
  background-color: transparent;
  padding-top: 2rem;
}

[data-name="faq-3"] .faq-question {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
  padding-top: 0px;
  padding-bottom: 0px;
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

@media (min-width: 1024px) {
  [data-name="faq-3"] .faq-question {
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

[data-name="faq-3"] .faq-question:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

[data-name="faq-3"] .faq-answer-inner {
  padding-bottom: 0px;
  padding-top: 1.5rem;
  padding-left: 2.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

@media (min-width: 1024px) {
  [data-name="faq-3"] .faq-answer-inner {
    padding-bottom: 0px;
    padding-top: 2rem;
    padding-left: 2.75rem;
  }
}

[data-name="faq-3"] .faq-answer-inner:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

[data-name="faq-4"] .faq-list {
  margin-left: auto;
  margin-right: auto;
  margin-top: 3rem;
  max-width: 64rem;
  gap: 2rem;
}

[data-name="faq-4"] .faq-list > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

[data-name="faq-4"] .faq-list {
  padding-left: 1rem;
  padding-right: 1rem;
}

#content-3 [data-name="faq-4"] .faq-list {
  align-items: center;
  -moz-column-gap: 4rem;
       column-gap: 4rem;
}

@media (min-width: 640px) {
  #content-3 [data-name="faq-4"] .faq-list {
    align-items: flex-start;
  }
}

@media (min-width: 1024px) {
  #content-3 [data-name="faq-4"] .faq-list {
    align-items: center;
  }
}

@media (min-width: 768px) {
  [data-name="faq-4"] .faq-list {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  [data-name="faq-4"] .faq-list > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

[data-name="faq-4"] .faq-item {
  border-width: 0px;
  padding-top: 1rem;
}

@media (min-width: 1024px) {
  [data-name="faq-4"] .faq-item {
    padding-top: 1.5rem;
  }
}

[data-name="faq-4"] .faq-question {
  border-width: 0px;
  padding-top: 0px;
  padding-bottom: 0px;
}

@media (min-width: 768px) {
  [data-name="faq-4"] .faq-question {
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

[data-name="faq-4"] .faq-answer-inner {
  margin-top: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

[data-name="faq-5"] .faq-list > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

[data-name="faq-7"] .faq-list {
  max-width: 80rem;
}

[data-name="faq-7"] .faq-item {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

[data-name="faq-8"] .faq-list {
  gap: 2rem;
}

#content-3 [data-name="faq-8"] .faq-list {
  align-items: center;
  -moz-column-gap: 4rem;
       column-gap: 4rem;
}

@media (min-width: 640px) {
  #content-3 [data-name="faq-8"] .faq-list {
    align-items: flex-start;
  }
}

@media (min-width: 1024px) {
  #content-3 [data-name="faq-8"] .faq-list {
    align-items: center;
  }
}

@media (min-width: 768px) {
  [data-name="faq-8"] .faq-list {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

[data-name="faq-8"] .faq-item {
  margin-bottom: 2rem;
  border-radius: 0.375rem;
  border-width: 1px;
  border-left-width: 8px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding: 1.5rem;
}

@media (min-width: 768px) {
  [data-name="faq-8"] .faq-item {
    margin-bottom: 0px;
    padding: 1.5rem;
  }
}

[data-name="faq-8"] .faq-question {
  border-width: 0px;
  padding-top: 0px;
  padding-bottom: 0px;
  font-weight: 700;
}

@media (min-width: 768px) {
  [data-name="faq-8"] .faq-question {
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

[data-name="faq-8"] .faq-answer-inner {
  margin-top: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.features-card-grid {
  margin-top: 3rem;
}

@media (min-width: 768px) {
  .features-card-grid {
    margin-top: 4rem;
  }
}

.features .section-heading {
  margin-bottom: 3rem;
}

@media (min-width: 768px) {
  .features .section-heading {
    margin-bottom: 4rem;
  }
}

.features-item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
}

.features-item .features-icon {
  font-size: 1.5rem;
  line-height: 2rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.features-item .features-title {
  margin: 0px;
  flex: 1 1 0%;
}

.features-item .features-text {
  width: 100%;
}

.features-item .features-text.content_section p {
  margin: 0px;
}

#features-2 .card-title {
  margin-top: 1rem;
}

#features-3 .features-card-grid {
  margin-top: 0px;
}

@media (min-width: 768px) {
  #features-3 .features-card-grid {
    margin-top: 0px;
  }
}

#features-3 .features-image {
  width: 48rem;
  max-width: none;
  border-radius: 0.75rem;
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(156 163 175 / 0.1);
}

@media (min-width: 640px) {
  #features-3 .features-image {
    width: 57rem;
  }
}

#features-4 .card-title {
  margin-top: 0.75rem;
}

#features-4 .card-icon {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

/* TODO: volgende regels moet naar utilities.helper-classes.css, bestands staat atm in andere feature-branch */

.hide-mobile-block {
  display: none;
}

@media (min-width: 768px) {
  .hide-mobile-block {
    display: block;
  }
}

.hide-mobile-flex {
  display: none;
}

@media (min-width: 768px) {
  .hide-mobile-flex {
    display: flex;
  }
}

.footer {
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

@media (min-width: 768px) {
  .footer {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

.footer-bottom {
  border-top-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

footer .footer-row {
  -moz-column-gap: 2.5rem;
       column-gap: 2.5rem;
  row-gap: 1.5rem;
}

footer .social-icons-row {
  margin-top: 1rem;
  -moz-column-gap: 2.5rem;
       column-gap: 2.5rem;
}

@media (min-width: 768px) {
  footer .social-icons-row {
    -moz-column-gap: 3.5rem;
         column-gap: 3.5rem;
  }
}

@media (min-width: 1024px) {
  footer .social-icons-row {
    margin-top: 0px;
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }
}

footer .social-media a {
  font-size: 1.5rem;
  line-height: 2rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

footer .social-media a:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  footer .social-media a {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  footer .social-media a {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

footer .navbar-logo img {
  max-height: 3rem;
}

footer .copyright,
footer .legal {
  margin-top: 1rem;
}

footer .copyright > :not([hidden]) ~ :not([hidden]),
footer .legal > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px) {
  footer .copyright,
footer .legal {
    margin-top: 0px;
  }

  footer .copyright > :not([hidden]) ~ :not([hidden]),
footer .legal > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

footer .copyright a:hover,
footer .legal a:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

footer .copyright-light,
footer .legal-light {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

footer .footer-row .footer-content > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px) {
  footer .footer-row .footer-content {
    margin-left: auto;
    margin-right: auto;
    max-width: 66.666%;
  }
}

@media (min-width: 1024px) {
  footer .footer-row .footer-content {
    max-width: 100%;
  }
}

footer .footermenu .menu-vertical a {
  text-transform: capitalize;
}

footer .footermenu .menu-header {
  display: flex;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

footer .footermenu .menu-header:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

footer .footer-contact > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

footer .footer-contact {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

footer .footer-contact a {
  display: flex;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

footer .footer-contact a:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

footer .footer-contact .footer-adres > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

footer .footer-contact .footer-adres {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

footer .footer-contact .footer-adres a {
  text-transform: lowercase;
}

footer .subscribe .footer-input-group {
  display: flex;
}

footer .subscribe input {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  border-top-width: 2px;
  border-bottom-width: 2px;
  border-left-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  line-height: 1;
}

footer .subscribe input::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(209 213 219 / var(--tw-placeholder-opacity));
}

footer .subscribe input::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(209 213 219 / var(--tw-placeholder-opacity));
}

footer .subscribe input:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

footer .subscribe button {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-transform: uppercase;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

footer .subscribe button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

@media (min-width: 768px) {
  footer .subscribe button {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

footer .footer-row .footer-content {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

footer .footer-row .buttons > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

footer .footer-row .buttons {
  padding-top: 1rem;
}

@media (min-width: 768px) {
  footer .footer-row .buttons > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
    --tw-space-x-reverse: 0;
    margin-right: calc(3rem * var(--tw-space-x-reverse));
    margin-left: calc(3rem * calc(1 - var(--tw-space-x-reverse)));
  }
}

@media (min-width: 1024px) {
  footer .footer-row .buttons {
    padding-top: 0px;
  }
}

footer .footer-row .button {
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

footer .footer-row .button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

@media (min-width: 768px) {
  footer .footer-row .button {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

footer .footer-row .subscribe-title {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

footer .footer-row .subscribe-title:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

footer .footer-row .subscribe-content {
  margin-top: 0.75rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

footer .footer-row .spacer {
  width: 66.666667%;
}

#footer-3 .social-icons-row {
  -moz-column-gap: 3.5rem;
       column-gap: 3.5rem;
}

@media (min-width: 768px) {
  #footer-3 .social-icons-row {
    -moz-column-gap: 2.5rem;
         column-gap: 2.5rem;
  }
}

#footer-5 .footer .footer-row {
  -moz-column-gap: 0px;
       column-gap: 0px;
}

#footer-5 .footer .footer-row > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(2px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(2px * var(--tw-divide-y-reverse));
  border-style: solid;
  --tw-divide-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-divide-opacity));
}

@media (min-width: 768px) {
  #footer-5 .footer .footer-row > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
  }
}

@media (min-width: 1024px) {
  #footer-5 .footer .footer-row {
    margin-left: -2rem;
    margin-right: -2rem;
  }

  #footer-5 .footer .footer-row > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(2px * var(--tw-divide-x-reverse));
    border-left-width: calc(2px * calc(1 - var(--tw-divide-x-reverse)));
  }
}

#footer-5 .footer-content,
#footer-5 .footermenu,
#footer-5 .footer-contact {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px) {
  #footer-5 .footer-content,
#footer-5 .footermenu,
#footer-5 .footer-contact {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  #footer-5 .footer-content,
#footer-5 .footermenu,
#footer-5 .footer-contact {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

#footer-5 .footermenu,
#footer-5 .logo,
#footer-5 .footer-contact {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

#footer-5 .footer-contact {
  padding-left: 2rem;
}

@media (min-width: 1024px) {
  #footer-5 .footer-contact {
    padding-left: 3rem;
  }
}

@media (min-width: 1280px) {
  #footer-5 .footer-contact {
    padding-left: 4rem;
  }
}

#footer-7 .footer-row {
  -moz-column-gap: 2.5rem;
       column-gap: 2.5rem;
  row-gap: 2.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
}

#footer-8 .footer-row > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px) {
  #footer-8 .footer-row {
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }

  #footer-8 .footer-row > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(-0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(-0px * var(--tw-space-y-reverse));
  }
}

@media (min-width: 1280px) {
  #footer-8 .footer-row {
    -moz-column-gap: 2.5rem;
         column-gap: 2.5rem;
  }
}

#footer-8 .subscribe-large {
  padding-top: 0.75rem;
}

@media (min-width: 768px) {
  #footer-8 .subscribe-large {
    border-top-width: 1px;
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
  }
}

@media (min-width: 1280px) {
  #footer-8 .subscribe-large {
    border-style: none;
    padding-top: 0px;
  }
}

@media (min-width: 768px) {
  #footer-8 .footer-row .subscribe-title {
    padding-bottom: 0px;
  }
}

@media (min-width: 1280px) {
  #footer-8 .footer-row .subscribe-title {
    padding-bottom: 0.5rem;
  }
}

@media (min-width: 768px) {
  #footer-8 .footer-row .subscribe-content {
    margin-top: 0px;
  }
}

@media (min-width: 1280px) {
  #footer-8 .footer-row .subscribe-content {
    margin-top: 0.75rem;
  }
}

#footer-8 .footer-row .subscribe {
  margin-top: 1rem;
}

@media (min-width: 768px) {
  #footer-8 .footer-row .subscribe {
    margin-top: 0px;
  }
}

@media (min-width: 1280px) {
  #footer-8 .footer-row .subscribe {
    margin-top: 1rem;
  }
}

@media (min-width: 1024px) {
  #footer-9 .footer-row .footer-content {
    width: 50%;
  }
}

@media (min-width: 1280px) {
  #footer-9 .footer-row .footer-content {
    width: 33.333333%;
  }
}

/* Form fields wrapper */

.form-wrapper {
  margin-left: -1rem;
  margin-right: -1rem;
  display: flex;
  flex-wrap: wrap;
}

/* Field wrapper */

.form-field-wrapper {
  position: relative;
  margin-bottom: 1.5rem;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

.form-field-wrapper .form-group {
  margin-top: 0.5rem;
}

/* Label */

.form-field-wrapper label.form-label {
  display: block;
  font-weight: 500;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.form-field-disabled label.field-label {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.form-field-wrapper label.checkbox-list-label {
  margin-bottom: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

/* Comment */

.form-field-wrapper .field-comment {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

/* Input, Textarea */

.form-field-wrapper .form-field {
  position: relative;
  border-radius: 0.375rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.form-field-wrapper .form-control {
  display: block;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 0px;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.form-field-wrapper .form-control::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.form-field-wrapper .form-control::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.form-field-wrapper .form-control:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity));
}

.form-field-wrapper .form-control:disabled {
  cursor: not-allowed;
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity));
}

@media (min-width: 640px) {
  .form-field-wrapper .form-control {
    font-size: 0.875rem;
    line-height: 1.5rem;
  }
}

/* Select/Dropdown */

.form-field-wrapper .form-select {
  margin-top: 0.5rem;
  display: block;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 0px;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.75rem;
  padding-right: 2.5rem;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.form-field-wrapper .form-select::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.form-field-wrapper .form-select::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.form-field-wrapper .form-select:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity));
}

.form-field-wrapper .form-select:disabled {
  cursor: not-allowed;
  border-color: rgb(209 213 219 / 0.7);
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

@media (min-width: 640px) {
  .form-field-wrapper .form-select {
    font-size: 0.875rem;
    line-height: 1.5rem;
  }
}

/* Checkbox & Radio wrap */

.form-field-wrapper .form-check-wrap, .form-field-wrapper .form-radio-wrap {
  position: relative;
  display: flex;
  align-items: flex-start;
}

.form-field-wrapper .form-check, .form-field-wrapper .form-radio {
  display: flex;
  height: 1.5rem;
  align-items: center;
}

/* Checkbox & Radio label */

.form-check-label-wrap {
  margin-left: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5rem;
}

.form-checkbox-list-wrap {
  margin-bottom: 0.5rem;
}

.form-field-wrapper .form-check-label,
.form-field-wrapper .field-radio-label {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.form-check-label-wrap .form-text {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.form-check-label-wrap .form-text a {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
  text-decoration-line: underline;
}

.form-check-label-wrap .form-text a:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.form-field-disabled .form-check-label, .form-field-disabled .form-radio-label {
  cursor: not-allowed;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

/* Checkbox & Radio input */

.form-check-input, .form-radio-input {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.form-check-input:focus, .form-radio-input:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity));
}

.form-check-input:disabled, .form-radio-input:disabled {
  cursor: not-allowed;
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

/* Radio input */

.form-field-wrapper input[type="radio"] {
  border-radius: 9999px;
}

/* Colorpicker */

.form-field-color {
  position: relative;
  display: flex;
}

.form-field-color .form-field-color-box {
  position: relative;
  display: block;
  height: 100%;
  width: 2.5rem;
}

.form-field-color .form-field-color-box .colorpicker-bg {
  pointer-events: none;
  position: absolute;
  inset: 0px;
}

.form-field-color input[type="color"] {
  position: absolute;
  inset: 0px;
  display: block;
  height: 2rem;
  width: 2.5rem;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-width: 0px;
}

.form-field-color .form-control {
  width: auto;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}

/* Submit button */

.form-submit-button {
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.form-submit-button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.form-submit-button:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: #3b82f6;
}

/* Validation */

.form-field-wrapper .form-control.is-invalid {
  display: block;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 0px;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-right: 2.5rem;
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(252 165 165 / var(--tw-ring-opacity));
}

.form-field-wrapper .form-control.is-invalid::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity));
}

.form-field-wrapper .form-control.is-invalid::placeholder {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity));
}

.form-field-wrapper .form-control.is-invalid:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

@media (min-width: 640px) {
  .form-field-wrapper .form-control.is-invalid {
    font-size: 0.875rem;
    line-height: 1.5rem;
  }
}

.form-field-wrapper .invalid-icon {
  display: none;
}

.form-field-wrapper .form-control.is-invalid ~ .invalid-icon {
  pointer-events: none;
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 0px;
  display: flex;
  align-items: center;
  padding-right: 0.75rem;
}

.form-field-wrapper .invalid-icon-svg {
  height: 1.25rem;
  width: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.form-field-wrapper .invalid-feedback {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.form-check-input.is-invalid {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}

.form-check-input.is-invalid:checked {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.form-field.has-error textarea {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}

.form-field-error-message {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

/* Horizontal form */

.form-horizontal .form-field-wrapper {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 1.5rem;
}

.form-horizontal .form-field-wrapper .form-label {
  grid-column: span 1 / span 1;
  padding-top: 0.375rem;
}

.form-horizontal .form-field-wrapper .form-group {
  grid-column: span 3 / span 3;
  margin-top: 0px;
}

@media (min-width: 768px) {
  .form-field-wrapper.row-1-2,
.form-field-wrapper.row-2-4 {
    width: 50%;
  }

  .form-field-wrapper.row-1-3 {
    width: 33.333333%;
  }

  .form-field-wrapper.row-2-3 {
    width: 66.666667%;
  }

  .form-field-wrapper.row-1-4 {
    width: 25%;
  }

  .form-field-wrapper.row-3-4 {
    width: 75%;
  }
}

/* New */

/* Form fields wrapper */

.form-wrapper {
  margin-left: -1rem;
  margin-right: -1rem;
  display: flex;
  flex-wrap: wrap;
}

.form-wrapper > div {
  margin-bottom: 1rem;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px) {
  .form-wrapper > .col-md-12 {
    width: 100%;
  }

  .form-wrapper > .col-md-11 {
    width: 91.666667%;
  }

  .form-wrapper > .col-md-10 {
    width: 83.333333%;
  }

  .form-wrapper > .col-md-9 {
    width: 75%;
  }

  .form-wrapper > .col-md-8 {
    width: 66.666667%;
  }

  .form-wrapper > .col-md-7 {
    width: 58.333333%;
  }

  .form-wrapper > .col-md-6 {
    width: 50%;
  }

  .form-wrapper > .col-md-5 {
    width: 41.666667%;
  }

  .form-wrapper > .col-md-4 {
    width: 33.333333%;
  }

  .form-wrapper > .col-md-3 {
    width: 25%;
  }

  .form-wrapper > .col-md-2 {
    width: 16.666667%;
  }

  .form-wrapper > .col-md-1 {
    width: 8.333333%;
  }
}

/* Field wrapper */

.form-field-wrapper {
  position: relative;
  margin-bottom: 1.5rem;
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px) {
  .form-field-wrapper.row-1-2,
.form-field-wrapper.row-2-4 {
    width: 50%;
  }

  .form-field-wrapper.row-1-3 {
    width: 33.333333%;
  }

  .form-field-wrapper.row-2-3 {
    width: 66.666667%;
  }

  .form-field-wrapper.row-1-4 {
    width: 25%;
  }

  .form-field-wrapper.row-3-4 {
    width: 75%;
  }
}

/* Form Label */

.form-wrapper .form-label {
  margin-bottom: 0.5rem;
  display: inline-block;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.dark .form-wrapper .form-label {
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.form-wrapper .form-text {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

/* Form Input & Select */

.form-wrapper .form-control,
.form-wrapper .form-select {
  display: block;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));
}

.form-wrapper .form-control:focus,
.form-wrapper .form-select:focus {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.dark .form-wrapper .form-control,
.dark .form-wrapper .form-select {
  display: block;
  width: 100%;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity));
}

.dark .form-wrapper .form-control:focus,
.dark .form-wrapper .form-select:focus {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

/* Form Checkbox */

.form-wrapper .form-check {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}

.form-wrapper .form-check .form-check-input {
  border-radius: 0.25rem;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
  --tw-ring-offset-width: 1px;
}

.form-wrapper .form-check .form-check-input:checked {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.form-wrapper .form-check .form-check-input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));
}

.form-wrapper .form-check input[type="radio"].form-check-input {
  border-radius: 9999px;
}

/* Form Upload */

.form-wrapper .form-control::file-selector-button {
  margin-top: -0.625rem;
  margin-bottom: -0.625rem;
  margin-left: -0.75rem;
  margin-right: 0.5rem;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-width: 0px;
  border-right-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.form-wrapper .form-control:hover::file-selector-button {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

/* Submit */

.form-wrapper .btn-primary {
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.form-wrapper .btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

/* Validation */

.form-wrapper .form-control.is-invalid {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
  background-repeat: no-repeat;
  padding-right: 1.5rem;
}

.form-wrapper .form-control.is-invalid:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(252 165 165 / var(--tw-ring-opacity));
}

.form-wrapper .form-control.is-invalid {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-wrapper textarea.form-control.is-invalid {
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.form-wrapper .invalid-feedback {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.form-wrapper .form-check .invalid-feedback {
  width: 100%;
}

/* Floating Labels */

.form-floating {
  position: relative;
}

.form-floating > input.form-control,
.form-floating > textarea.form-control {
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.form-floating > label {
  position: absolute;
  left: 0.75rem;
  top: 1rem;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.form-floating > input.form-control:not(:-moz-placeholder-shown), .form-floating > textarea.form-control:not(:-moz-placeholder-shown) {
  padding-top: 1.5rem;
  padding-bottom: 0.5rem;
}

.form-floating > input.form-control:focus,
.form-floating > textarea.form-control:focus,
.form-floating > input.form-control:not(:placeholder-shown),
.form-floating > textarea.form-control:not(:placeholder-shown),
.form-floating > .form-select {
  padding-top: 1.5rem;
  padding-bottom: 0.5rem;
}

.form-floating > input.form-control:not(:-moz-placeholder-shown) ~ label, .form-floating > textarea.form-control:not(:-moz-placeholder-shown) ~ label {
  top: 0.625rem;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.form-floating > input.form-control:focus ~ label,
.form-floating > textarea.form-control:focus ~ label,
.form-floating > input.form-control:not(:placeholder-shown) ~ label,
.form-floating > textarea.form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  top: 0.625rem;
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .form-horizontal .form-wrapper > div,
.form-horizontal .form-wrapper .form-button-wrap {
    display: flex;
    flex-wrap: wrap;
  }
}

.form-horizontal .form-wrapper .form-label,
.form-horizontal .form-wrapper .form-button-wrap .form-empty-space {
  position: relative;
  top: 0.5rem;
  width: 100%;
}

@media (min-width: 768px) {
  .form-horizontal .form-wrapper .form-label,
.form-horizontal .form-wrapper .form-button-wrap .form-empty-space {
    width: 25%;
    padding-right: 1rem;
    text-align: right;
  }
}

.form-horizontal .form-wrapper .form-control,
.form-horizontal .form-wrapper .form-select,
.form-horizontal .form-wrapper .form-button-wrap .form-button {
  width: 100%;
}

@media (min-width: 768px) {
  .form-horizontal .form-wrapper .form-control,
.form-horizontal .form-wrapper .form-select,
.form-horizontal .form-wrapper .form-button-wrap .form-button {
    width: 75%;
  }

  .form-horizontal .form-wrapper .invalid-feedback,
.form-horizontal .form-wrapper .form-text {
    width: 100%;
    text-align: right;
  }
}

.upload-field-wrap {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
  display: flex;
}

.upload-field-wrap > div {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.upload-field-wrap ul.upload-file-list {
  margin-top: 0.5rem;
}

.upload-field-wrap ul.upload-file-list > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity));
}

.upload-field-wrap ul.upload-file-list {
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.upload-field-wrap ul.upload-file-list:empty {
  display: none;
}

.upload-field-wrap ul.upload-file-list .uploaded-file {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

/* Gallery Boxes */

.gallery-wrap .gallery-intro {
  margin-left: auto;
  margin-right: auto;
  margin-top: 1.5rem;
  max-width: 36rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Project Slider */

.portfolio-project-slider {
  margin-top: 2rem;
}

@media (min-width: 768px) {
  .portfolio-project-slider {
    margin-top: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .portfolio-project-slider {
    margin-top: 1rem;
  }
}

.pf-slider-main-item {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 768px) {
  .pf-slider-main-item {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .pf-slider-main-item {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}

.pf-slider-main-item img {
  border-radius: 0.375rem;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.pf-slider-trail-wrap {
  align-items: center;
}

@media (min-width: 640px) {
  .pf-slider-trail-wrap {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }
}

.pf-trail-prev,
.pf-trail-next {
  cursor: pointer;
  font-size: 1.875rem;
  line-height: 2.25rem;
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.pf-trail-prev:hover,
.pf-trail-next:hover {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.pf-trail-next {
  text-align: right;
}

.pf-slider-trail-col {
  grid-column: span 10 / span 10;
}

.pf-slider-trail {
  max-width: 100%;
}

.pf-slider-trail-item {
  padding: 0.75rem;
}

.pf-slider-trail-item img {
  border-radius: 0.25rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.gallery-wrap .slick-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity));
}

.gallery-wrap .slick-dots:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.gallery-wrap .slick-dots li {
  display: flex;
}

.gallery-wrap .slick-dots button {
  height: 0.5rem;
  width: 1.5rem;
  border-radius: 9999px;
  font-size: 0px;
  line-height: 0;
  color: transparent;
}

@media (min-width: 768px) {
  .gallery-wrap .slick-dots button {
    width: 3rem;
  }
}

.gallery-wrap .slick-dots .slick-active button {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.gallery-wrap .slick-dots .slick-active button:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.gallery-slider-control {
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: 1.5rem;
  line-height: 2rem;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.gallery-slider-control:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.gallery-slider-control:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.gallery-slider-control:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.gallery-6-wrap .gallery-slider-control-wrap {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  opacity: 0.5;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.gallery-6-wrap .gallery-slider-control-wrap:hover {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  opacity: 1;
}

.gallery-6-wrap .gallery-6-slider-prev-wrap {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.gallery-6-wrap .gallery-6-slider-next-wrap {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.gallery-6-wrap .gallery-slider-control {
  cursor: pointer;
  font-size: 3rem;
  line-height: 1;
  color: rgb(255 255 255 / 0.7);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.gallery-6-wrap .gallery-slider-control:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.gallery-6-wrap .slick-dots {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.gallery-6-wrap .slick-dots:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.gallery-6-wrap .slick-dots .slick-active button {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.gallery-6-wrap .slick-dots .slick-active button:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.gallery-8-wrap .gallery-slider-control-wrap {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  opacity: 0.5;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.gallery-8-wrap .gallery-slider-control-wrap:hover {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  opacity: 1;
}

.gallery-8-wrap .gallery-8-slider-prev-wrap {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.gallery-8-wrap .gallery-8-slider-next-wrap {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.gallery-8-wrap .gallery-slider-control {
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: 3rem;
  line-height: 1;
  color: rgb(255 255 255 / 0.7);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.gallery-8-wrap .gallery-slider-control:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.gallery-8-wrap .slick-dots {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.gallery-8-wrap .slick-dots .slick-active button {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.row-grid-all {
  display: grid;
}

@media (min-width: 640px) {
  .row-grid-sm {
    display: grid;
  }
}

@media (min-width: 768px) {
  .row-grid {
    display: grid;
  }
}

@media (min-width: 1024px) {
  .row-grid-lg {
    display: grid;
  }
}

.row-flex-all {
  margin-left: -1rem;
  margin-right: -1rem;
  display: flex;
}

@media (min-width: 640px) {
  .row-flex-sm {
    margin-left: -1rem;
    margin-right: -1rem;
    display: flex;
  }
}

@media (min-width: 768px) {
  .row-flex {
    margin-left: -1rem;
    margin-right: -1rem;
    display: flex;
  }
}

@media (min-width: 1024px) {
  .row-flex-lg {
    margin-left: -1rem;
    margin-right: -1rem;
    display: flex;
  }
}

.row-flex-col {
  padding-left: 1rem;
  padding-right: 1rem;
}

.row-flex-no-gap {
  margin-left: 0px;
  margin-right: 0px;
}

@media (min-width: 640px) {
  .row-flex-no-gap {
    margin-left: 0px;
    margin-right: 0px;
  }
}

@media (min-width: 768px) {
  .row-flex-no-gap {
    margin-left: 0px;
    margin-right: 0px;
  }
}

@media (min-width: 1024px) {
  .row-flex-no-gap {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.row-flex-no-gap .row-flex-col {
  padding-left: 0px;
  padding-right: 0px;
}

.maintenance-page {
  height: 100vh;
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.maintenance-page .maintenance-card {
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  text-align: center;
}

.maintenance-page .maintenance-adres a:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

/* Navbar menu's */

.mainmenu {
  position: relative;
  z-index: 40;
}

@media (min-width: 768px) {
  .mainmenu > ul {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .mainmenu > ul {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }
}

.mainmenu > ul > li {
  position: relative;
}

.mainmenu > ul > li > p {
  display: flex;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.mainmenu > ul > li > p:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.mainmenu > ul > li > a {
  display: flex;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.mainmenu > ul > li > a:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.mainmenu > ul > li.active > a {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.mainmenu > ul > li.active > a:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.mainmenu > ul > li > ul {
  position: absolute;
  top: 100%;
  display: none;
  width: 16rem;
  --tw-translate-x: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  overflow: hidden;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.mainmenu > ul > li:hover > ul {
  z-index: 50;
  display: block;
}

.mainmenu > ul > li:hover > ul > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity));
}

.mainmenu > ul > li > ul > li > a {
  display: block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.mainmenu > ul > li > ul > li > a:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.navbar-bottom .mainmenu ul li a {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.navbar-bottom .mainmenu ul li a:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

/* Footer menu's */

.footermenu > ul {
  flex-wrap: wrap;
  -moz-column-gap: 2rem;
       column-gap: 2rem;
  row-gap: 0.5rem;
}

.footermenu > ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px) {
  .footermenu > ul {
    display: flex;
    justify-content: center;
  }

  .footermenu > ul > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

@media (min-width: 1024px) {
  .footermenu > ul {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }
}

@media (min-width: 1280px) {
  .footermenu > ul {
    justify-content: flex-end;
  }
}

.footermenu > ul > li > a {
  display: block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: center;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.footermenu > ul > li > a:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .footermenu > ul > li > a {
    text-align: left;
  }
}

/* Mobile menu's */

.mobilemenu-wrapper {
  position: fixed;
  inset: 0px;
  background-color: rgb(55 65 81 / 0.4);
}

.mobilemenu-inner {
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 0px;
  width: 80%;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 1rem;
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (min-width: 768px) {
  .mobilemenu-inner {
    width: 33.333333%;
  }
}

.mobilemenu-wrapper .close-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.mobilemenu-content {
  display: flow-root;
  padding-top: 1.5rem;
}

.mobilemenu-content .mobilemenu {
  margin-top: -1.5rem;
  margin-bottom: -1.5rem;
}

.mobilemenu-content .mobilemenu > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  border-color: rgb(107 114 128 / 0.1);
}

.mobilemenu > ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.mobilemenu > ul {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.mobilemenu > ul > li {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
  display: block;
}

.mobilemenu > ul > li.hasSubmenu > .mobilemenu-item {
  display: flex;
  justify-content: space-between;
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}

.mobilemenu > ul > li > a,
.mobilemenu > ul > li > .mobilemenu-item > a {
  display: block;
  border-radius: 0.5rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.mobilemenu > ul > li.active > a {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.mobilemenu > ul > li > ul.submenu > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.mobilemenu > ul > li > ul.submenu {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.25rem;
  padding-bottom: 0.75rem;
}

.mobilemenu-contact {
  margin-left: -1rem;
  margin-right: -1rem;
  margin-top: auto;
}

.mobilemenu-contact > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.mobilemenu-contact {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.mobilemenu-contact .navbar-logo img {
  max-height: 2.5rem;
}

.mobilemenu-contact a {
  display: inline-flex;
  align-items: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

/* Atomic */

#main-nav > ul {
  display: flex;
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}

@media (min-width: 1024px) {
  #main-nav > ul {
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }
}

@media (min-width: 1280px) {
  #main-nav > ul {
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
  }
}

@media (min-width: 1536px) {
  #main-nav > ul {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }
}

#main-nav > ul > li {
  align-self: center;
}

#main-nav > ul > li > .nav-item {
  display: flex;
  align-items: center;
}

#main-nav > ul > li > .nav-item > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

#main-nav > ul > li > .nav-item {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-weight: 500;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

#main-nav > ul > li > .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.group\/menuitem:hover #main-nav > ul > li > .nav-item {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

#main-nav > ul > li > .nav-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

#main-nav > ul > li > .nav-item:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.group\/menuitem:hover #main-nav > ul > li > .nav-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

#main-nav > ul > li > span.nav-item {
  cursor: default;
}

#main-nav > ul > li > .nav-item > .parent-icon {
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 1;
}

#main-nav > ul > li.active > .nav-item {
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

#main-nav > ul > li.active > .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

#main-nav > ul > li.active > .nav-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

#main-nav > ul > li.active > .nav-item:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

#top-nav > ul {
  display: flex;
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

#top-nav > ul > li {
  align-self: center;
}

#top-nav > ul > li > .nav-item {
  display: flex;
  align-items: center;
}

#top-nav > ul > li > .nav-item > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

#top-nav > ul > li > .nav-item {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-weight: 500;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

#top-nav > ul > li > .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.group\/menuitem:hover #top-nav > ul > li > .nav-item {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

#top-nav > ul > li > .nav-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

#top-nav > ul > li > .nav-item:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.group\/menuitem:hover #top-nav > ul > li > .nav-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

#top-nav > ul > li > span.nav-item {
  cursor: default;
}

#top-nav > ul > li > .nav-item > .parent-icon {
  display: none;
}

#top-nav > ul > li.active > .nav-item {
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

#top-nav > ul > li.active > .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

#top-nav > ul > li.active > .nav-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

#top-nav > ul > li.active > .nav-item:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

#mobile-nav ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

#mobile-nav > ul > li {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  align-items: center;
}

#mobile-nav > ul > li > .nav-item {
  display: flex;
  align-items: center;
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

#mobile-nav > ul > li > .parent-icon,
#mobile-nav > ul > li > .nav-item > .parent-icon {
  margin-left: 2rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

#mobile-nav > ul > li > ul > li > .nav-item {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  #mobile-nav > ul > li > ul > li > .nav-item {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

#mobile-nav ul.mobile-topmenu > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

#mobile-nav ul.mobile-topmenu > li > .nav-item {
  padding-top: 0px;
  padding-bottom: 0px;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

[data-name="navbar-8"] #main-nav > ul {
  display: flex;
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

@media (min-width: 1280px) {
  [data-name="navbar-8"] #main-nav > ul {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }
}

@media (min-width: 1536px) {
  [data-name="navbar-8"] #main-nav > ul {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }
}

footer .footer-menu .nav-item {
  display: inline-flex;
  font-size: 1.125rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

footer .footer-menu .nav-item:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.group\/menuitem:hover footer .footer-menu .nav-item {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  footer .footer-menu .nav-item {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1024px) {
  footer .footer-menu .nav-item {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

footer .footer-menu .nav-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

footer .footer-menu .nav-item:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

footer .footer-menu > ul {
  display: flex;
  flex-direction: column;
}

footer .footer-menu > ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px) {
  footer .footer-menu > ul {
    flex-direction: row;
    align-items: center;
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
  }

  footer .footer-menu > ul > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

@media (min-width: 1024px) {
  footer .footer-menu > ul {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }
}

footer .footer-menu > ul > li {
  text-align: center;
}

@media (min-width: 768px) {
  footer .footer-menu > ul > li {
    text-align: left;
  }
}

footer[data-name="footer-4"] .footer-menu > ul,
footer[data-name="footer-5"] .footer-menu > ul,
footer[data-name="footer-8"] .footer-menu > ul {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

footer[data-name="footer-4"] .footer-menu > ul > :not([hidden]) ~ :not([hidden]),
footer[data-name="footer-5"] .footer-menu > ul > :not([hidden]) ~ :not([hidden]),
footer[data-name="footer-8"] .footer-menu > ul > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px) {
  footer[data-name="footer-4"] .footer-menu > ul,
footer[data-name="footer-5"] .footer-menu > ul,
footer[data-name="footer-8"] .footer-menu > ul {
    flex-direction: column;
    align-items: flex-start;
  }

  footer[data-name="footer-4"] .footer-menu > ul > :not([hidden]) ~ :not([hidden]),
footer[data-name="footer-5"] .footer-menu > ul > :not([hidden]) ~ :not([hidden]),
footer[data-name="footer-8"] .footer-menu > ul > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
  }
}

footer[data-name="footer-4"] .footer-menu > ul {
  align-items: center;
}

@media (min-width: 768px) {
  footer[data-name="footer-4"] .footer-menu > ul {
    align-items: center;
  }
}

footer[data-name="footer-8"] .footer-menu > ul > li {
  text-align: left;
  line-height: 1.625;
}

@media (min-width: 768px) {
  footer[data-name="footer-8"] .footer-menu > ul > li {
    text-align: left;
  }
}

footer[data-name="footer-4"] .footer-menu > ul > li {
  text-align: center;
  line-height: 1.625;
}

@media (min-width: 768px) {
  footer[data-name="footer-4"] .footer-menu > ul > li {
    align-items: center;
  }
}

footer[data-name="footer-4"] .footer-menu .nav-item,
footer[data-name="footer-5"] .footer-menu .nav-item,
footer[data-name="footer-8"] .footer-menu .nav-item {
  font-size: 1rem;
  line-height: 1.5rem;
}

@media (min-width: 768px) {
  footer[data-name="footer-4"] .footer-menu .nav-item,
footer[data-name="footer-5"] .footer-menu .nav-item,
footer[data-name="footer-8"] .footer-menu .nav-item {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 1024px) {
  footer[data-name="footer-4"] .footer-menu .nav-item,
footer[data-name="footer-5"] .footer-menu .nav-item,
footer[data-name="footer-8"] .footer-menu .nav-item {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.navbar {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.navbar-top {
  border-bottom-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding-top: 1rem;
  padding-bottom: 1rem;
}

@media (min-width: 768px) {
  .navbar-top {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
}

.navbar-bottom {
  border-top-width: 2px;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

@media (min-width: 768px) {
  .navbar .navbar-row {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }
}

.navbar .logo {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

@media (min-width: 768px) {
  .navbar .logo {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
}

.navbar .logo img {
  max-height: 2rem;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: left;
     object-position: left;
}

@media (min-width: 1024px) {
  .navbar .logo img {
    max-height: 2.5rem;
  }
}

.navbar-buttons {
  display: none;
}

@media (min-width: 768px) {
  .navbar-buttons {
    display: block;
  }
}

.navbar-buttons a.link {
  display: none;
  text-transform: uppercase;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.navbar-buttons a.link:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .navbar-buttons a.link {
    display: inline-block;
  }
}

.navbar-buttons a.button {
  display: none;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 2rem;
  padding-right: 2rem;
}

.navbar-buttons a.button:hover {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity));
}

@media (min-width: 768px) {
  .navbar-buttons a.button {
    display: flex;
  }
}

.navbar .contact-info address {
  display: flex;
  -moz-column-gap: 2rem;
       column-gap: 2rem;
  font-style: normal;
}

.navbar .contact-info address a {
  display: inline-flex;
  height: 2rem;
  width: 2rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.navbar .contact-info address a:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .navbar .contact-info address a {
    height: auto;
    width: auto;
    justify-content: flex-start;
    border-radius: 0px;
    background-color: transparent;
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity));
  }
}

.navbar .contact-info address a span {
  margin-left: 0.375rem;
  display: none;
}

@media (min-width: 768px) {
  .navbar .contact-info address a span {
    display: block;
  }
}

.navbar-top .contact-info address {
  display: flex;
  -moz-column-gap: 2rem;
       column-gap: 2rem;
  font-style: normal;
}

.navbar-top .contact-info address a {
  display: inline-flex;
  align-items: center;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.navbar-top .contact-info address a:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.navbar-top .contact-info address a span {
  margin-left: 0.375rem;
}

.navbar .search {
  border-left-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding-left: 1rem;
}

@media (min-width: 768px) {
  .navbar .search {
    padding-left: 2rem;
  }
}

.navbar .search-icon {
  font-size: 1.25rem;
  line-height: 1.75rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.navbar .search-icon:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.navbar-top .social-media a {
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.navbar-top .social-media a:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

#navbar-5 .navbar-row {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

@media (min-width: 1024px) {
  #navbar-5 .navbar-row {
    -moz-column-gap: 4rem;
         column-gap: 4rem;
  }
}

#navbar-8 .navbar .navbar-row {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

#navbar-9 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

#navbar-9 .navbar {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
  padding-top: 1rem;
  padding-bottom: 1rem;
}

#navbar-9 .navbar .navbar-row {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

#navbar-10 .navbar .navbar-row {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

.openingtimes-table {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

@media (min-width: 640px) {
  .openingtimes-table {
    margin-left: 0px;
    margin-right: 0px;
    border-radius: 0.5rem;
  }
}

.openingtimes-table table {
  min-width: 100%;
}

.openingtimes-table table tr {
  border-bottom-width: 1px;
}

.openingtimes-table table tr:last-of-type {
  border-width: 0px;
}

.openingtimes-table th {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1rem;
  padding-right: 0.75rem;
  text-align: left;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

@media (min-width: 640px) {
  .openingtimes-table th {
    padding-left: 1.5rem;
  }
}

.openingtimes-table td {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  text-align: left;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.sticky_nav {
  position: fixed;
  left: 0px;
  right: 0px;
  top: 0px;
  z-index: 999;
}

.hero-navbar.hero-dark .navbar {
  background-color: rgb(255 255 255 / 0.5);
}

.hero-navbar.is-sticky.hero-dark .navbar {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.hero-navbar.is-sticky.hero-light .navbar {
  border-bottom-width: 1px;
}

@media (min-width: 1280px) {
  .hero-wide .container {
    max-width: 1536px;
  }
}

.hero-navbar.hero-dark .navbar-logo.hero-logo-light {
  display: block;
}

.hero-navbar.hero-dark .navbar-logo.hero-logo-dark {
  display: none;
}

.hero-navbar.hero-dark.is-sticky .navbar-logo.hero-logo-light {
  display: none;
}

.hero-navbar.hero-dark.is-sticky .navbar-logo.hero-logo-dark {
  display: block;
}

.hero-navbar.hero-light .navbar-logo.hero-logo-light,
.hero-navbar.hero-light.is-sticky .navbar-logo.hero-logo-light {
  display: none;
}

.hero-navbar.hero-light .navbar-logo.hero-logo-dark,
.hero-navbar.hero-light.is-sticky .navbar-logo.hero-logo-dark {
  display: block;
}

/* Navbar menu's */

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu {
  position: relative;
  z-index: 40;
}

@media (min-width: 768px) {
  .hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li {
  position: relative;
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li > a {
  display: flex;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  text-transform: uppercase;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li > a:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li.active > a {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li.active > a:hover {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li > ul {
  position: absolute;
  top: 100%;
  display: none;
  width: 16rem;
  overflow: hidden;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li:hover > ul {
  z-index: 50;
  display: block;
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li:hover > ul > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li > ul > li > a {
  display: block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li > ul > li > a:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .navbar-bottom .mainmenu ul li a {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .navbar-bottom .mainmenu ul li a:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

/* Navbar buttons */

.hero-navbar.not-sticky.hero-dark .navbar .navbar-buttons a.link {
  display: none;
  text-transform: uppercase;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .navbar-buttons a.link:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .hero-navbar.not-sticky.hero-dark .navbar .navbar-buttons a.link {
    display: inline-block;
  }
}

.hero-navbar.not-sticky.hero-dark .navbar .navbar-buttons a.button {
  display: none;
  border-radius: 0.375rem;
  border-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 2rem;
  padding-right: 2rem;
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.hero-navbar.not-sticky.hero-dark .navbar .navbar-buttons a.button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .hero-navbar.not-sticky.hero-dark .navbar .navbar-buttons a.button {
    display: flex;
  }
}

.howitworks {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 768px) {
  .howitworks {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 1024px) {
  .howitworks {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

.howitworks .hiw-items {
  margin-top: 2rem;
}

@media (min-width: 768px) {
  .howitworks .hiw-items {
    margin-top: 4rem;
  }
}

.howitworks .hiw-items .hiw-item {
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.howitworks .hiw-items .hiw-item .hiw-icon {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 2rem;
  display: flex;
  aspect-ratio: 4/3;
  width: 33.333333%;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity));
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.howitworks .hiw-items .hiw-item .hiw-content {
  text-align: center;
}

.howitworks .hiw-items .hiw-item .hiw-content p {
  margin: 0px;
}

.hiw-horizontal {
  margin-top: 2rem;
}

@media (min-width: 1024px) {
  .hiw-horizontal {
    margin-top: 4rem;
  }
}

.hiw-horizontal .hiw-item {
  display: grid;
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

@media (min-width: 1024px) {
  .hiw-horizontal .hiw-item {
    display: block;
    width: 25%;
    text-align: center;
  }
}

.hiw-horizontal .hiw-item .hiw-item-img {
  grid-column: span 2 / span 2;
  display: none;
  justify-content: center;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .hiw-horizontal .hiw-item .hiw-item-img {
    display: flex;
  }
}

@media (min-width: 1024px) {
  .hiw-horizontal .hiw-item .hiw-item-img {
    margin-bottom: 1rem;
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

.hiw-horizontal .hiw-item .hiw-item-img img {
  height: 8rem;
  width: 8rem;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity));
}

.hiw-horizontal .hiw-item .hiw-item-step {
  position: relative;
  display: flex;
  width: 100%;
  justify-content: center;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .hiw-horizontal .hiw-item .hiw-item-step {
    align-items: center;
  }
}

@media (min-width: 1024px) {
  .hiw-horizontal .hiw-item .hiw-item-step {
    margin-bottom: 1.5rem;
    align-items: flex-start;
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

.hiw-horizontal .hiw-item .hiw-item-step .hiw-item-orb {
  position: relative;
  z-index: 20;
  height: 2rem;
  width: 2rem;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity));
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  display: flex;
  align-items: center;
  justify-content: center;
}

.hiw-horizontal .hiw-item .hiw-item-step .hiw-item-line {
  top: 0px;
  bottom: 0px;
  display: none;
  width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(253 224 71 / var(--tw-bg-opacity));
}

@media (min-width: 768px) {
  .hiw-horizontal .hiw-item .hiw-item-step .hiw-item-line {
    position: absolute;
    display: block;
  }
}

@media (min-width: 1024px) {
  .hiw-horizontal .hiw-item .hiw-item-step .hiw-item-line {
    top: auto;
    bottom: auto;
    left: 0px;
    top: 50%;
    height: 1px;
    width: 100%;
  }
}

.hiw-horizontal .hiw-item .hiw-item-step .hiw-item-line-first {
  top: 50%;
  bottom: 0px;
}

@media (min-width: 1024px) {
  .hiw-horizontal .hiw-item .hiw-item-step .hiw-item-line-first {
    top: 50%;
    bottom: 0px;
    left: 50%;
    width: 50%;
  }
}

.hiw-horizontal .hiw-item .hiw-item-step .hiw-item-line-last {
  bottom: 50%;
  top: 0px;
}

@media (min-width: 1024px) {
  .hiw-horizontal .hiw-item .hiw-item-step .hiw-item-line-last {
    top: 50%;
    bottom: auto;
    left: 0px;
    width: 50%;
  }
}

.hiw-horizontal .hiw-item .hiw-item-content {
  grid-column: span 5 / span 5;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .hiw-horizontal .hiw-item .hiw-item-content {
    grid-column: span 3 / span 3;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

@media (min-width: 1024px) {
  .hiw-horizontal .hiw-item .hiw-item-content {
    display: block;
    padding-top: 0px;
    padding-bottom: 0px;
  }
}

.hiw-horizontal .hiw-item .hiw-item-title {
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity));
}

.hiw-horizontal .hiw-item .hiw-item-text {
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}

#howitworks-1 .hiw-buttons {
  text-align: center;
}

#howitworks-2 .hiw-buttons {
  margin-top: 3rem;
}

@media (min-width: 768px) {
  #howitworks-2 .hiw-buttons {
    text-align: center;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .hiw-buttons {
    text-align: left;
  }
}

#howitworks-2 .content_section {
  margin-top: 3rem;
}

@media (min-width: 768px) {
  #howitworks-2 .content_section {
    text-align: center;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .content_section {
    text-align: left;
  }
}

#howitworks-2 .box-steps {
  margin-top: 4rem;
}

@media (min-width: 1024px) {
  #howitworks-2 .box-steps {
    margin-top: 0px;
  }
}

#howitworks-2 .box-steps .box-steps-step {
  display: flex;
  width: 100%;
}

@media (min-width: 768px) {
  #howitworks-2 .box-steps .box-steps-step {
    position: static;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .box-steps .box-steps-step {
    display: flex;
  }
}

#howitworks-2 .box-steps .box-steps-steplist {
  display: flex;
}

@media (min-width: 768px) {
  #howitworks-2 .box-steps .box-steps-steplist {
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .box-steps .box-steps-steplist {
    flex-direction: column;
  }
}

#howitworks-2 .box-steps .box-steps-orb-wrap {
  position: relative;
  margin-bottom: 0.75rem;
  display: flex;
  height: auto;
  width: 100%;
  justify-content: center;
}

@media (min-width: 768px) {
  #howitworks-2 .box-steps .box-steps-orb-wrap {
    margin-bottom: 0.75rem;
    width: 100%;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .box-steps .box-steps-orb-wrap {
    margin-bottom: 0px;
    height: 100%;
    width: 3rem;
  }
}

#howitworks-2 .box-steps .box-steps-step-orb {
  position: relative;
  z-index: 20;
  display: flex;
  height: 3rem;
  width: 3rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  border-style: none;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
}

#howitworks-2 .box-steps .box-steps-step-line {
  position: absolute;
  z-index: 10;
  display: none;
  height: 1px;
  width: 100%;
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity));
}

@media (min-width: 640px) {
  #howitworks-2 .box-steps .box-steps-step-line {
    display: block;
  }
}

@media (min-width: 768px) {
  #howitworks-2 .box-steps .box-steps-step-line {
    top: 50%;
    height: 1px;
    width: 100%;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .box-steps .box-steps-step-line {
    top: auto;
    height: 100%;
    width: 1px;
  }
}

#howitworks-2 .box-steps .step-line-first {
  left: 50%;
  width: 50%;
}

@media (min-width: 768px) {
  #howitworks-2 .box-steps .step-line-first {
    left: 50%;
    width: 50%;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .box-steps .step-line-first {
    left: auto;
    width: 1px;
  }
}

#howitworks-2 .box-steps .step-line-last {
  left: 0px;
  width: 50%;
}

@media (min-width: 768px) {
  #howitworks-2 .box-steps .step-line-last {
    left: 0px;
    width: 50%;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .box-steps .step-line-last {
    display: none;
    width: 1px;
  }
}

#howitworks-2 .box-steps .box-steps-step-title {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: left;
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  #howitworks-2 .box-steps .box-steps-step-title {
    padding-left: 1rem;
    padding-right: 1rem;
    text-align: center;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .box-steps .box-steps-step-title {
    padding-left: 0px;
    padding-right: 0px;
    text-align: left;
  }
}

#howitworks-2 .box-steps .box-steps-step-text {
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: left;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  #howitworks-2 .box-steps .box-steps-step-text {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    padding-left: 1rem;
    padding-right: 1rem;
    text-align: center;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .box-steps .box-steps-step-text {
    overflow: visible;
    display: block;
    -webkit-box-orient: horizontal;
    -webkit-line-clamp: none;
    padding-left: 0px;
    padding-right: 0px;
    text-align: left;
  }
}

.instagram-slider-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

@media (min-width: 768px) {
  .instagram-slider-wrapper {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
}

.ig-slider-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ig-slider-buttons .ig-slider-button {
  display: flex;
  height: 4rem;
  width: 4rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
  font-size: 1.5rem;
  line-height: 2rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.ig-slider-buttons .ig-slider-button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.ig-slider-buttons-prev {
  order: 2;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px) {
  .ig-slider-buttons-prev {
    order: 0;
    padding-left: 0px;
    padding-right: 0px;
  }
}

.ig-slider-buttons-next {
  order: 3;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px) {
  .ig-slider-buttons-next {
    order: 0;
    padding-left: 0px;
    padding-right: 0px;
  }
}

.ig-slider-wrapper {
  width: 100%;
}

@media (min-width: 768px) {
  .ig-slider-wrapper {
    grid-column: span 10 / span 10;
    width: auto;
  }
}

.ig-slider {
  order: 1;
  width: 100%;
  max-width: 100%;
}

@media (min-width: 768px) {
  .ig-slider {
    order: 0;
  }
}

.ig-slider .feed-item-outer {
  padding: 1rem;
  padding-bottom: 1.5rem;
}

.ig-slider .feed-item-inner {
  overflow: hidden;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.ig-slider .feed-item-thumb {
  position: relative;
  display: block;
  aspect-ratio: 4 / 3;
  width: 100%;
}

.ig-slider .feed-item-thumb .ig-image {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.ig-slider .feed-item-thumb .ig-watermark {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 2rem;
}

.ig-slider .feed-item-content {
  padding: 1rem;
}

.ig-slider .feed-item-caption {
  margin-bottom: 1rem;
  border-bottom-width: 1px;
  padding-bottom: 1rem;
}

.ig-slider .feed-item-caption h1 {
  margin-bottom: 1rem;
}

.ig-slider .feed-item-caption h2 {
  margin-bottom: 0.75rem;
}

.ig-slider .feed-item-caption h3 {
  margin-bottom: 0.5rem;
}

.ig-slider .feed-item-caption h1 a,.ig-slider .feed-item-caption h2 a,.ig-slider .feed-item-caption h3 a {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.ig-slider .feed-item-caption h1 a:hover,.ig-slider .feed-item-caption h2 a:hover,.ig-slider .feed-item-caption h3 a:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
  text-decoration-line: underline;
}

.ig-slider .feed-item-caption h1 a,.ig-slider .feed-item-caption h2 a,.ig-slider .feed-item-caption h3 a {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.ig-slider .feed-item-caption h1 a:hover,.ig-slider .feed-item-caption h2 a:hover,.ig-slider .feed-item-caption h3 a:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
  text-decoration-line: underline;
}

.ig-slider .feed-item-caption h1 a,.ig-slider .feed-item-caption h2 a,.ig-slider .feed-item-caption h3 a {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.ig-slider .feed-item-caption h1 a:hover,.ig-slider .feed-item-caption h2 a:hover,.ig-slider .feed-item-caption h3 a:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
  text-decoration-line: underline;
}

.ig-slider .feed-item-caption h1 b,.ig-slider .feed-item-caption h1 strong,.ig-slider .feed-item-caption h2 b,.ig-slider .feed-item-caption h2 strong,.ig-slider .feed-item-caption h3 b,.ig-slider .feed-item-caption h3 strong {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.ig-slider .feed-item-caption h4 {
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .ig-slider .feed-item-caption h4 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.ig-slider .feed-item-caption h1.success_heading,.ig-slider .feed-item-caption h2.success_heading,.ig-slider .feed-item-caption h3.success_heading,.ig-slider .feed-item-caption h4.success_heading {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}

.ig-slider .feed-item-caption p {
  margin-bottom: 1.5rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.ig-slider .feed-item-caption p a:not(.btn):not(.btn-lg) {
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.ig-slider .feed-item-caption p a:not(.btn):not(.btn-lg):hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
  text-decoration-line: underline;
}

.ig-slider .feed-item-caption img {
  max-width: 100%;
}

.ig-slider .feed-item-caption ul,.ig-slider .feed-item-caption ol {
  margin-bottom: 1rem;
}

.ig-slider .feed-item-caption ul > :not([hidden]) ~ :not([hidden]),.ig-slider .feed-item-caption ol > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.ig-slider .feed-item-caption ul,.ig-slider .feed-item-caption ol {
  padding-left: 1rem;
  margin-bottom: 1rem;
}

.ig-slider .feed-item-caption ul > :not([hidden]) ~ :not([hidden]),.ig-slider .feed-item-caption ol > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.ig-slider .feed-item-caption ul,.ig-slider .feed-item-caption ol {
  padding-left: 1rem;
}

.ig-slider .feed-item-caption ul {
  list-style-type: disc;
}

.ig-slider .feed-item-caption ul *::marker {
  color: rgb(234 179 8 );
}

.ig-slider .feed-item-caption ul::marker {
  color: rgb(234 179 8 );
}

.ig-slider .feed-item-caption ol {
  list-style-type: decimal;
}

.ig-slider .feed-item-caption li {
  font-size: 1rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.ig-slider .feed-item-caption table {
  width: 100%;
  vertical-align: top;
}

.ig-slider .feed-item-caption table tr {
  vertical-align: top;
}

.ig-slider .feed-item-caption table td,.ig-slider .feed-item-caption table th {
  vertical-align: top;
}

.ig-slider .feed-item-caption blockquote {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-left-width: 4px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
  padding: 1rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 500;
  font-style: italic;
  line-height: 1.625;
}

.ig-slider .feed-item-caption blockquote p {
  margin: 0px;
}

.ig-slider .feed-item-caption blockquote p::before {
  --tw-content: '“';
  content: var(--tw-content);
}

.ig-slider .feed-item-caption blockquote p::after {
  --tw-content: '„';
  content: var(--tw-content);
}

.ig-slider .feed-item-caption blockquote blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.dark .ig-slider .feed-item-caption h2,
.dark .ig-slider .feed-item-caption h3 {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .ig-slider .feed-item-caption p {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity));
}

.dark .ig-slider .feed-item-caption h4 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.dark .ig-slider .feed-item-caption blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark .ig-slider .feed-item-caption blockquote blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.light .ig-slider .feed-item-caption h2 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.light .ig-slider .feed-item-caption h3 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.light .ig-slider .feed-item-caption p {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.light .ig-slider .feed-item-caption h4 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.light .ig-slider .feed-item-caption blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.light .ig-slider .feed-item-caption blockquote blockquote {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.ig-slider .feed-item-caption.p-lg p {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.ig-slider .feed-item-caption.p-xl p {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.blog-item .blog-item-excerpt.ig-slider .feed-item-caption p {
  margin: 0px;
}

.features-item .features-text.ig-slider .feed-item-caption p {
  margin: 0px;
}

#howitworks-2 .ig-slider .feed-item-caption {
  margin-top: 3rem;
}

@media (min-width: 768px) {
  #howitworks-2 .ig-slider .feed-item-caption {
    text-align: center;
  }
}

@media (min-width: 1024px) {
  #howitworks-2 .ig-slider .feed-item-caption {
    text-align: left;
  }
}

.ig-slider .feed-item-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.ig-slider .feed-item-details .feed-item-details-profile {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.ig-slider .feed-item-details .feed-item-details-profile:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.ig-slider .feed-item-details .feed-item-details-date {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.pagination-wrapper {
  margin-top: 3rem;
  display: flex;
  justify-content: center;
}

.pagination {
  isolation: isolate;
  display: inline-flex;
}

.pagination > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}

.pagination {
  border-radius: 0.375rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.pagination .page-item .page-link {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-inset: inset;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.pagination .page-item .page-link:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.pagination .page-item .page-link:focus {
  z-index: 20;
  outline-offset: 0px;
}

.pagination .page-item.active .page-link {
  z-index: 10;
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.pagination .page-item.active .page-link:focus {
  z-index: 20;
}

.pagination .page-item.active .page-link:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: #2563eb;
}

.pagination .page-item.first .page-link {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.pagination .page-item.last .page-link {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.pagination .page-item.disabled .page-link {
  cursor: default;
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

/* Portfolio Boxes */

.portfolio .portfolio-intro {
  margin-left: auto;
  margin-right: auto;
  margin-top: 1.5rem;
  max-width: 36rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

.pf-tabs {
  margin-top: 3.5rem;
}

.pf-tabs .pf-tab-list {
  display: flex;
  width: 100%;
  justify-content: center;
}

@media (min-width: 768px) {
  .pf-tabs .pf-tab-list {
    width: auto;
  }
}

.pf-tabs .pf-tab-list-items {
  width: 100%;
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
  border-radius: 0.5rem;
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding: 0.125rem;
}

@media (min-width: 640px) {
  .pf-tabs .pf-tab-list-items {
    display: flex;
  }
}

@media (min-width: 768px) {
  .pf-tabs .pf-tab-list-items {
    width: auto;
    justify-content: center;
  }
}

.pf-tabs .pf-tab-list-items .pf-tab-list-button {
  display: block;
  width: 100%;
  border-radius: 0.375rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-weight: 700;
}

@media (min-width: 640px) {
  .pf-tabs .pf-tab-list-items .pf-tab-list-button {
    display: inline;
    width: auto;
    flex: 1 1 0%;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .pf-tabs .pf-tab-list-items .pf-tab-list-button {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .pf-tabs .pf-tab-list-items .pf-tab-list-button {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }
}

.pf-tabs .pf-tab {
  margin-top: 2rem;
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1rem;
}

@media (min-width: 768px) {
  .pf-tabs .pf-tab {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .pf-tabs .pf-tab {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.pf-tabs .pf-tab .pf-tab-footer {
  grid-column: span 2 / span 2;
  display: flex;
  justify-content: center;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 1024px) {
  .pf-tabs .pf-tab .pf-tab-footer {
    grid-column: span 3 / span 3;
  }
}

/* Portfolio Project Item */

.pf-project {
  position: relative;
  overflow: hidden;
  border-radius: 0.375rem;
}

.pf-project:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.pf-project img {
  position: relative;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.pf-project .pf-project-title {
  position: absolute;
  left: 1rem;
  right: 1rem;
  bottom: 1rem;
  border-radius: 0.75rem;
  border-width: 1px;
  border-color: rgb(255 255 255 / 0.1);
  background-color: rgb(255 255 255 / 0.2);
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: 2rem;
  padding-right: 2rem;
  text-align: center;
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.pf-project-sm .pf-project-title {
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

/* Portfolio Category */

#portfolio-category {
}

@media (min-width: 640px) {
  .pf-project-grid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;
  }
}

@media (min-width: 768px) {
  .pf-project-grid {
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .pf-project-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.pf-project-grid .pf-project:hover {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.pf-project-grid .pf-project .pf-project-img-link {
  position: relative;
  display: block;
  overflow: hidden;
  border-radius: 0.375rem;
}

.pf-project-grid .pf-project .project-title,
.pf-project-grid .pf-project .project-button {
  margin-top: 1.5rem;
}

.pf-project-grid .pf-project .project-desc {
  margin-top: 0.5rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Portfolio Detail */

.portfolio-project-page {
  overflow: hidden;
}

@media (min-width: 1024px) {
  .pf-project-page-layout {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 2rem;
  }
}

.pf-project-content {
  grid-column: span 2 / span 2;
  border-radius: 0.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .pf-project-content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.pf-page-intro {
  margin-top: 1.5rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.pf-project-description {
  margin-top: 2rem;
}

@media (min-width: 1024px) {
  .pf-project-description {
    margin-top: 3.5rem;
  }
}

.project-navigation {
  margin-top: 2rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  -moz-column-gap: 2rem;
       column-gap: 2rem;
  border-top-width: 1px;
  padding-top: 1rem;
}

.pf-project-aside {
  position: relative;
}

.pf-project-aside-overlay {
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: -50%;
  width: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

@media (min-width: 1024px) {
  .pf-project-aside-overlay {
    left: 0px;
  }
}

.pf-project-aside-items {
  position: relative;
}

.pf-project-aside-items > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  --tw-divide-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-divide-opacity));
}

.pf-project-aside-items {
  padding-top: 3rem;
  padding-bottom: 3rem;
  padding-left: 2rem;
  padding-right: 2rem;
}

@media (min-width: 768px) {
  .pf-project-aside-items {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
  }

  .pf-project-aside-items > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
  }
}

@media (min-width: 1024px) {
  .pf-project-aside-items {
    display: block;
  }

  .pf-project-aside-items > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
    --tw-divide-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-divide-opacity));
  }
}

.pf-project-aside-items .pf-project {
  margin-bottom: 1.5rem;
  padding-top: 1.5rem;
}

.pf-project-aside-items .pf-project:hover {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (min-width: 768px) {
  .pf-project-aside-items .pf-project {
    padding-top: 0px;
  }
}

@media (min-width: 1024px) {
  .pf-project-aside-items .pf-project {
    padding-top: 1.5rem;
  }
}

.pf-project-aside-items .pf-project-first {
  padding-top: 0px;
}

@media (min-width: 768px) {
  .pf-project-aside-items .pf-project-first {
    padding-top: 0px;
  }
}

@media (min-width: 1024px) {
  .pf-project-aside-items .pf-project-first {
    padding-top: 0px;
  }
}

.pf-project-aside-items .pf-project .pf-project-img-link {
  position: relative;
  display: block;
  overflow: hidden;
  border-radius: 0.375rem;
}

.pf-project-aside-items .pf-project .pf-project-img-link img {
  aspect-ratio: 16 / 9;
  border-radius: 0.375rem;
}

.pf-project-aside-items .pf-project .project-title,
.pf-project-aside-items .pf-project .project-button {
  margin-top: 1.5rem;
}

.pf-project-aside-items .pf-project .project-desc {
  margin-top: 0.5rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Project Slider */

.portfolio-project-slider {
  margin-top: 2rem;
}

@media (min-width: 768px) {
  .portfolio-project-slider {
    margin-top: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .portfolio-project-slider {
    margin-top: 1rem;
  }
}

.pf-slider-main-item {
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
  padding-top: 2rem;
  padding-bottom: 2rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px) {
  .pf-slider-main-item {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .pf-slider-main-item {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
}

.pf-slider-main-item img {
  border-radius: 0.375rem;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.pf-slider-trail-wrap {
  display: grid;
  grid-template-columns: repeat(12, minmax(0, 1fr));
  align-items: center;
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

.pf-trail-prev,
.pf-trail-next {
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: 1.875rem;
  line-height: 2.25rem;
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.pf-trail-prev:hover,
.pf-trail-next:hover {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.pf-trail-next {
  text-align: right;
}

.pf-slider-trail-col {
  grid-column: span 10 / span 10;
}

.pf-slider-trail {
  max-width: 100%;
}

.pf-slider-trail-item {
  padding: 0.75rem;
}

.pf-slider-trail-item img {
  border-radius: 0.25rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.reviews .reviews-intro {
  margin-left: auto;
  margin-right: auto;
  margin-top: 1.5rem;
  max-width: 56rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Slider Boxes */

.slider-wrap .slider-intro {
  margin-left: auto;
  margin-right: auto;
  margin-top: 1.5rem;
  max-width: 36rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* .slider-wrap .slick-dots { @apply bg-primary-highlight dark:bg-gray-800 flex justify-center items-center gap-2 rounded-full w-auto; } */

/* .slider-wrap .slick-dots li { @apply text-primary; } */

/* .slider-wrap .slick-dots button { @apply w-6 md:w-12 h-2 rounded-full text-[0px] leading-[0] text-transparent; }
.slider-wrap .slick-dots .slick-active button { @apply bg-primary dark:bg-primary-darker; }
.slider-slider-control { @apply text-2xl text-primary hover:text-primary-hover transition-all cursor-pointer dark:text-primary-darker dark:hover:text-primary select-none; } */

.slider-6-wrap .slider-slider-control-wrap {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  opacity: 0.5;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.slider-6-wrap .slider-slider-control-wrap:hover {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  opacity: 1;
}

.slider-6-wrap .slider-6-slider-prev-wrap {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.slider-6-wrap .slider-6-slider-next-wrap {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.slider-6-wrap .slider-slider-control {
  cursor: pointer;
  font-size: 3rem;
  line-height: 1;
  color: rgb(255 255 255 / 0.7);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.slider-6-wrap .slider-slider-control:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.slider-6-wrap .slick-dots {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.slider-6-wrap .slick-dots:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.slider-6-wrap .slick-dots .slick-active button {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.slider-6-wrap .slick-dots .slick-active button:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.slider-8-wrap .slider-slider-control-wrap {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  opacity: 0.5;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.slider-8-wrap .slider-slider-control-wrap:hover {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  opacity: 1;
}

.slider-8-wrap .slider-8-slider-prev-wrap {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.slider-8-wrap .slider-8-slider-next-wrap {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.slider-8-wrap .slider-slider-control {
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  font-size: 3rem;
  line-height: 1;
  color: rgb(255 255 255 / 0.7);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.slider-8-wrap .slider-slider-control:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.slider-8-wrap .slick-dots {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.slider-8-wrap .slick-dots .slick-active button {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.team-tabs .team-tab-list {
  display: flex;
  width: 100%;
  justify-content: center;
}

@media (min-width: 1280px) {
  .team-tabs .team-tab-list {
    width: auto;
  }
}

.team-tabs .team-tab-list-items {
  width: 100%;
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
  border-radius: 1rem;
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding: 0.125rem;
}

@media (min-width: 768px) {
  .team-tabs .team-tab-list-items {
    display: flex;
    width: auto;
    justify-content: center;
    border-radius: 9999px;
  }
}

.team-tabs .team-tab-list-items .team-tab-list-button {
  display: block;
  width: 100%;
  border-radius: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-weight: 700;
}

@media (min-width: 640px) {
  .team-tabs .team-tab-list-items .team-tab-list-button {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .team-tabs .team-tab-list-items .team-tab-list-button {
    display: inline;
    width: auto;
    flex: 1 1 0%;
    border-radius: 9999px;
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
}

.team-tabs .team-tab {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.team-tabs .team-tab > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px) {
  .team-tabs .team-tab {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .team-tabs .team-tab > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

.team-tabs .team-tab .team-tab-footer {
  grid-column: span 2 / span 2;
  display: flex;
  justify-content: center;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 1024px) {
  .team-tabs .team-tab .team-tab-footer {
    grid-column: span 3 / span 3;
  }
}

.box-child-form {
  margin-top: 2rem;
}

.box-stats {
  width: 16rem;
}

.box-stats > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

@media (min-width: 1280px) {
  .box-stats {
    width: 20rem;
  }
}

.box-stats-label {
  font-size: 1rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

@media (min-width: 1024px) {
  .box-stats-label {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.box-stats-value {
  font-size: 3rem;
  line-height: 1;
  font-weight: 600;
  letter-spacing: -0.025em;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.dark .box-stats-label {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.dark .box-stats-value {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.box-steps {
}

.box-steps .box-steps-steplist {
  margin-top: 2.5rem;
  display: flex;
  flex-direction: column;
}

.box-steps .box-steps-orb-wrap {
  position: relative;
  display: flex;
  height: 100%;
  width: 4rem;
  justify-content: center;
}

.box-steps .box-steps-step-orb {
  position: relative;
  z-index: 20;
  display: flex;
  height: 4rem;
  width: 4rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity));
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.box-steps .box-steps-step-line {
  position: absolute;
  z-index: 10;
  height: 100%;
  width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.box-steps .box-steps-step-title {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.box-steps .box-steps-step-text {
  font-size: 1rem;
  line-height: 1.5rem;
}

.dark .box-steps .box-steps-step-orb {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .box-steps .box-steps-step-line {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.dark .box-steps .box-steps-step-title {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark .box-steps .box-steps-step-text {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.bullet {
  display: flex;
}

.bullet > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.bullet {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.bullet:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.bullet .bullet-icon {
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.bullet .bullet-icon:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity));
}

.bullet .bullet-icon i {
  line-height: 1.75rem;
}

.bullet .bullet-text {
  line-height: 1.75rem;
}

.bullet .bullet-text:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity));
}

.bullet .bullet-text p {
  line-height: 1.75rem;
}

[data-name="example-1"] .bullet .bullet-icon {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity));
}

[data-name="example-1"] .bullet .bullet-icon:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}

[data-name="example-2"] .bullet .bullet-icon {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

[data-name="example-2"] .bullet .bullet-icon:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.buttons-wrap {
  margin-top: 2rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.card-atomic {
  display: flex;
  height: 100%;
  flex: 1 1 0%;
  flex-direction: column;
}

.card-atomic.card-basic {
  gap: 2rem;
  padding: 2rem;
}

/*.card-atomic.card-sm { @apply ; }*/

.card-atomic.card-lg {
  gap: 1rem;
  padding: 2rem;
}

.card-atomic.card-icon {
  gap: 2rem;
  padding: 2rem;
}

[data-name="example-1"] .card-atomic.card-basic {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity));
}

[data-name="example-1"] .card-atomic.card-basic:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}

[data-name="example-2"] .card-atomic.card-basic {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

[data-name="example-2"] .card-atomic.card-basic:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.secondary-title-color div:has(.prose) :is(:where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.secondary-title-color div:has(.prose) :is(:where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.secondary-title-color div:has(.prose) :is(:where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.secondary-title-color div:has(.prose) :is(:where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.features-grid > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

@media (min-width: 768px) {
  .features-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 2rem;
  }

  .features-grid > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}

.feature {
  display: flex;
  flex-direction: column;
  row-gap: 0.75rem;
}

.feature .feature-icon {
  font-size: 2.25rem;
  line-height: 2.5rem;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.feature .feature-icon:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

[data-name="example-1"] .feature .feature-icon {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

[data-name="example-1"] .feature .feature-icon:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

[data-name="features-10"] .feature-title h3 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

[data-name="features-10"] .feature-title h3:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.form-image-wrapper {
  overflow: hidden;
  border-radius: 1rem;
  border-width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (min-width: 768px) {
  .form-image-grid {
    display: grid;
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }

  .form-image-img {
    grid-column: span 3 / span 3;
  }
}

.form-image-heading {
  border-bottom-width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 2rem;
  padding-right: 2rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 1;
}

.form-image-heading a {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.form-image-heading a:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .form-image-content {
    grid-column: span 7 / span 7;
  }
}

.form-image-footer {
  border-top-width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 2rem;
  padding-right: 2rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 1;
}

.form-image-footer a {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.form-image-footer a:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.no-image .form-image-img {
  display: none;
}

@media (min-width: 768px) {
  .no-image .form-image-content {
    grid-column: span 10 / span 10;
  }
}

.heading-span p {
  margin-top: 0px;
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
}

.heading-span p strong {
  font-weight: 700;
}

.heading-span.member-name p {
  margin-bottom: 0px;
}

[data-category="team"] .heading-span.member-name p {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 700;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

[data-category="team"] .heading-span.member-title p {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
}

[data-name="team-5"] .heading-span.member-name p,
[data-name="team-10"] .heading-span.member-name p {
  font-size: 1.5rem;
  line-height: 2rem;
}

[data-name="team-5"] .heading-span.member-title p,
[data-name="team-10"] .heading-span.member-title p {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

[data-name="team-6"] .social-item svg,
[data-name="team-7"] .social-item svg,
[data-name="team-8"] .social-item svg {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

[data-name="team-6"] .social-item svg:hover,
[data-name="team-7"] .social-item svg:hover,
[data-name="team-8"] .social-item svg:hover {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

[data-name="team-6"] .heading-span.member-name p,
[data-name="team-7"] .heading-span.member-name p,
[data-name="team-8"] .heading-span.member-name p {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

/* Sidebar */

.tree-navigation {
  padding: 1rem;
}

.tree-root a {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
}

.tree-root a:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.tree-root a:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tree-root a:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.tree-root a.active {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.tree-root a.active:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.tree-root a.active:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.tree-root a.active:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.sub-item {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
}

.sub-item:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.sub-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.sub-item:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.sub-item.active {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.sub-item.active:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.sub-item.active:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.sub-item.active:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.sub-sub-item:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.sub-sub-item:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.sub-sub-item:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.sub-sub-item.active {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.sub-sub-item.active:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.sub-sub-item.active:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.sub-sub-item.active:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

/* Content */

[data-name="text-navigation-sidebar"] .content-heading h2,
[data-name="text-navigation-sidebar"] .content-heading h4 {
  margin-top: 0px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

.visible {
  visibility: visible;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.-inset-y-3 {
  top: -0.75rem;
  bottom: -0.75rem;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-x-6 {
  left: 1.5rem;
  right: 1.5rem;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-bottom-24 {
  bottom: -6rem;
}

.-left-0\.5 {
  left: -0.125rem;
}

.-left-2 {
  left: -0.5rem;
}

.-left-4 {
  left: -1rem;
}

.-right-0\.5 {
  right: -0.125rem;
}

.-top-4 {
  top: -1rem;
}

.bottom-0 {
  bottom: 0px;
}

.bottom-12 {
  bottom: 3rem;
}

.bottom-5 {
  bottom: 1.25rem;
}

.bottom-8 {
  bottom: 2rem;
}

.left-0 {
  left: 0px;
}

.left-1\/2 {
  left: 50%;
}

.left-4 {
  left: 1rem;
}

.left-8 {
  left: 2rem;
}

.right-0 {
  right: 0px;
}

.right-1\/2 {
  right: 50%;
}

.right-2 {
  right: 0.5rem;
}

.right-8 {
  right: 2rem;
}

.top-0 {
  top: 0px;
}

.top-1\/2 {
  top: 50%;
}

.top-14 {
  top: 3.5rem;
}

.top-2 {
  top: 0.5rem;
}

.top-3\.5 {
  top: 0.875rem;
}

.top-4 {
  top: 1rem;
}

.top-6 {
  top: 1.5rem;
}

.top-8 {
  top: 2rem;
}

.top-px {
  top: 1px;
}

.isolate {
  isolation: isolate;
}

.-z-10 {
  z-index: -10;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-\[51\] {
  z-index: 51;
}

.z-\[9999\] {
  z-index: 9999;
}

.z-\[999\] {
  z-index: 999;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-first {
  order: -9999;
}

.order-last {
  order: 9999;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-10 {
  grid-column: span 10 / span 10;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-5 {
  grid-column: span 5 / span 5;
}

.col-span-6 {
  grid-column: span 6 / span 6;
}

.col-span-8 {
  grid-column: span 8 / span 8;
}

.col-span-9 {
  grid-column: span 9 / span 9;
}

.col-start-2 {
  grid-column-start: 2;
}

.row-span-1 {
  grid-row: span 1 / span 1;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-span-3 {
  grid-row: span 3 / span 3;
}

.row-span-4 {
  grid-row: span 4 / span 4;
}

.float-right {
  float: right;
}

.-m-0\.5 {
  margin: -0.125rem;
}

.-m-2 {
  margin: -0.5rem;
}

.m-0 {
  margin: 0px;
}

.m-10 {
  margin: 2.5rem;
}

.m-2 {
  margin: 0.5rem;
}

.m-4 {
  margin: 1rem;
}

.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-mx-4 {
  margin-left: -1rem;
  margin-right: -1rem;
}

.-mx-px {
  margin-left: -1px;
  margin-right: -1px;
}

.-my-2 {
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
}

.-my-3 {
  margin-top: -0.75rem;
  margin-bottom: -0.75rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-24 {
  margin-top: 6rem;
  margin-bottom: 6rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.-mb-10 {
  margin-bottom: -2.5rem;
}

.-mb-12 {
  margin-bottom: -3rem;
}

.-mb-px {
  margin-bottom: -1px;
}

.-ml-12 {
  margin-left: -3rem;
}

.-ml-16 {
  margin-left: -4rem;
}

.-ml-px {
  margin-left: -1px;
}

.-mr-1 {
  margin-right: -0.25rem;
}

.-mr-2 {
  margin-right: -0.5rem;
}

.-mr-48 {
  margin-right: -12rem;
}

.-mr-96 {
  margin-right: -24rem;
}

.-mt-1\.5 {
  margin-top: -0.375rem;
}

.-mt-12 {
  margin-top: -3rem;
}

.-mt-4 {
  margin-top: -1rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-9 {
  margin-bottom: 2.25rem;
}

.ml-0\.5 {
  margin-left: 0.125rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.ml-8 {
  margin-left: 2rem;
}

.ml-auto {
  margin-left: auto;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-6 {
  margin-right: 1.5rem;
}

.mr-auto {
  margin-right: auto;
}

.mt-0 {
  margin-top: 0px;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-14 {
  margin-top: 3.5rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-20 {
  margin-top: 5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-9 {
  margin-top: 2.25rem;
}

.mt-auto {
  margin-top: auto;
}

.box-content {
  box-sizing: content-box;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

.line-clamp-5 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

.line-clamp-6 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.flow-root {
  display: flow-root;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.aspect-\[2\/3\] {
  aspect-ratio: 2/3;
}

.aspect-\[3\/2\] {
  aspect-ratio: 3/2;
}

.aspect-\[3\/4\] {
  aspect-ratio: 3/4;
}

.aspect-\[6\/5\] {
  aspect-ratio: 6/5;
}

.aspect-auto {
  aspect-ratio: auto;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-thumb {
  aspect-ratio: 4 / 3;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.h-0\.5 {
  height: 0.125rem;
}

.h-1\/2 {
  height: 50%;
}

.h-1\/3 {
  height: 33.333333%;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-14 {
  height: 3.5rem;
}

.h-20 {
  height: 5rem;
}

.h-24 {
  height: 6rem;
}

.h-28 {
  height: 7rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-40 {
  height: 10rem;
}

.h-48 {
  height: 12rem;
}

.h-5 {
  height: 1.25rem;
}

.h-56 {
  height: 14rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-96 {
  height: 24rem;
}

.h-\[180px\] {
  height: 180px;
}

.h-\[240px\] {
  height: 240px;
}

.h-\[300px\] {
  height: 300px;
}

.h-\[450px\] {
  height: 450px;
}

.h-\[500px\] {
  height: 500px;
}

.h-auto {
  height: auto;
}

.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.h-2 {
  height: 0.5rem;
}

.max-h-12 {
  max-height: 3rem;
}

.max-h-16 {
  max-height: 4rem;
}

.max-h-28 {
  max-height: 7rem;
}

.max-h-32 {
  max-height: 8rem;
}

.max-h-72 {
  max-height: 18rem;
}

.max-h-96 {
  max-height: 24rem;
}

.max-h-\[320px\] {
  max-height: 320px;
}

.max-h-full {
  max-height: 100%;
}

.min-h-\[466px\] {
  min-height: 466px;
}

.min-h-full {
  min-height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.w-0 {
  width: 0px;
}

.w-0\.5 {
  width: 0.125rem;
}

.w-1\/2 {
  width: 50%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-1\/5 {
  width: 20%;
}

.w-1\/6 {
  width: 16.666667%;
}

.w-10 {
  width: 2.5rem;
}

.w-10\/12 {
  width: 83.333333%;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-2\/5 {
  width: 40%;
}

.w-20 {
  width: 5rem;
}

.w-28 {
  width: 7rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: 1rem;
}

.w-40 {
  width: 10rem;
}

.w-44 {
  width: 11rem;
}

.w-48 {
  width: 12rem;
}

.w-5 {
  width: 1.25rem;
}

.w-5\/6 {
  width: 83.333333%;
}

.w-56 {
  width: 14rem;
}

.w-6 {
  width: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.w-\[200\%\] {
  width: 200%;
}

.w-\[40\%\] {
  width: 40%;
}

.w-\[48rem\] {
  width: 48rem;
}

.w-\[60\%\] {
  width: 60%;
}

.w-\[600px\] {
  width: 600px;
}

.w-\[68\.5625rem\] {
  width: 68.5625rem;
}

.w-\[98\%\] {
  width: 98%;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.w-px {
  width: 1px;
}

.w-screen {
  width: 100vw;
}

.w-2 {
  width: 0.5rem;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-52 {
  max-width: 13rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[1440px\] {
  max-width: 1440px;
}

.max-w-full {
  max-width: 100%;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-min {
  max-width: -moz-min-content;
  max-width: min-content;
}

.max-w-none {
  max-width: none;
}

.max-w-screen-xl {
  max-width: 1280px;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.shrink-0 {
  flex-shrink: 0;
}

.flex-grow {
  flex-grow: 1;
}

.origin-top-right {
  transform-origin: top right;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-12 {
  --tw-translate-y: -3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-16 {
  --tw-translate-y: -4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-\[100\%\] {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1\/2 {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-24 {
  --tw-translate-x: 6rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-5 {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-7 {
  --tw-translate-x: 1.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-2 {
  --tw-translate-y: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-\[100\%\] {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-180 {
  --tw-rotate: -180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.skew-x-\[-30deg\] {
  --tw-skew-x: -30deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-90 {
  --tw-scale-x: .9;
  --tw-scale-y: .9;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-default {
  cursor: default;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

.resize {
  resize: both;
}

.scroll-py-2 {
  scroll-padding-top: 0.5rem;
  scroll-padding-bottom: 0.5rem;
}

.scroll-py-3 {
  scroll-padding-top: 0.75rem;
  scroll-padding-bottom: 0.75rem;
}

.list-inside {
  list-style-position: inside;
}

.list-\[circle\] {
  list-style-type: circle;
}

.list-disc {
  list-style-type: disc;
}

.grid-flow-row {
  grid-auto-flow: row;
}

.auto-rows-max {
  grid-auto-rows: max-content;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.content-center {
  align-content: center;
}

.content-end {
  align-content: flex-end;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-0 {
  gap: 0px;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-16 {
  gap: 4rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-20 {
  gap: 5rem;
}

.gap-24 {
  gap: 6rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-px {
  gap: 1px;
}

.gap-x-0 {
  -moz-column-gap: 0px;
       column-gap: 0px;
}

.gap-x-12 {
  -moz-column-gap: 3rem;
       column-gap: 3rem;
}

.gap-x-14 {
  -moz-column-gap: 3.5rem;
       column-gap: 3.5rem;
}

.gap-x-16 {
  -moz-column-gap: 4rem;
       column-gap: 4rem;
}

.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}

.gap-x-3 {
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}

.gap-x-32 {
  -moz-column-gap: 8rem;
       column-gap: 8rem;
}

.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

.gap-x-8 {
  -moz-column-gap: 2rem;
       column-gap: 2rem;
}

.gap-y-0 {
  row-gap: 0px;
}

.gap-y-10 {
  row-gap: 2.5rem;
}

.gap-y-12 {
  row-gap: 3rem;
}

.gap-y-16 {
  row-gap: 4rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-3 {
  row-gap: 0.75rem;
}

.gap-y-32 {
  row-gap: 8rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.gap-y-8 {
  row-gap: 2rem;
}

.-space-x-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(3rem * var(--tw-space-x-reverse));
  margin-left: calc(3rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-10 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}

.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

.space-y-14 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3.5rem * var(--tw-space-y-reverse));
}

.space-y-16 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-20 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-black\/20 > :not([hidden]) ~ :not([hidden]) {
  border-color: rgb(0 0 0 / 0.2);
}

.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity));
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity));
}

.divide-gray-300 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-divide-opacity));
}

.divide-gray-400 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-divide-opacity));
}

.divide-gray-500 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-divide-opacity));
}

.divide-secondary-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-divide-opacity));
}

.self-center {
  align-self: center;
}

.justify-self-end {
  justify-self: end;
}

.justify-self-center {
  justify-self: center;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hyphens-auto {
  -webkit-hyphens: auto;
          hyphens: auto;
}

.whitespace-normal {
  white-space: normal;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-balance {
  text-wrap: balance;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-none {
  border-radius: 0px;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-l-3xl {
  border-top-left-radius: 1.5rem;
  border-bottom-left-radius: 1.5rem;
}

.rounded-l-full {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}

.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-4 {
  border-width: 4px;
}

.border-\[3px\] {
  border-width: 3px;
}

.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.border-y-2 {
  border-top-width: 2px;
  border-bottom-width: 2px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity));
}

.border-black\/20 {
  border-color: rgb(0 0 0 / 0.2);
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}

.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}

.border-gray-800\/10 {
  border-color: rgb(31 41 55 / 0.1);
}

.border-gray-900\/10 {
  border-color: rgb(17 24 39 / 0.1);
}

.border-gray-900\/20 {
  border-color: rgb(17 24 39 / 0.2);
}

.border-gray-900\/25 {
  border-color: rgb(17 24 39 / 0.25);
}

.border-gray-900\/5 {
  border-color: rgb(17 24 39 / 0.05);
}

.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity));
}

.border-primary-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity));
}

.border-primary-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.border-primary-500\/30 {
  border-color: rgb(59 130 246 / 0.3);
}

.border-primary-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}

.border-primary-dark {
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity));
}

.border-red-100 {
  --tw-border-opacity: 1;
  border-color: rgb(254 226 226 / var(--tw-border-opacity));
}

.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}

.border-secondary-300 {
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity));
}

.border-secondary-500\/20 {
  border-color: rgb(234 179 8 / 0.2);
}

.border-transparent {
  border-color: transparent;
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.border-zinc-200 {
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity));
}

.border-b-blue-700 {
  --tw-border-opacity: 1;
  border-bottom-color: rgb(29 78 216 / var(--tw-border-opacity));
}

.border-opacity-10 {
  --tw-border-opacity: 0.1;
}

.bg-amber-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}

.bg-amber-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-black\/10 {
  background-color: rgb(0 0 0 / 0.1);
}

.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

.bg-gray-400\/20 {
  background-color: rgb(156 163 175 / 0.2);
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.bg-gray-600\/50 {
  background-color: rgb(75 85 99 / 0.5);
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity));
}

.bg-green-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity));
}

.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity));
}

.bg-lime-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity));
}

.bg-lime-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(101 163 13 / var(--tw-bg-opacity));
}

.bg-neutral-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(38 38 38 / var(--tw-bg-opacity));
}

.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity));
}

.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}

.bg-orange-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity));
}

.bg-pink-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity));
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.bg-primary-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
}

.bg-primary-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity));
}

.bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity));
}

.bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.bg-primary-500\/70 {
  background-color: rgb(59 130 246 / 0.7);
}

.bg-primary-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.bg-primary-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity));
}

.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}

.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}

.bg-secondary-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity));
}

.bg-secondary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}

.bg-secondary-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity));
}

.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity));
}

.bg-sky-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}

.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}

.bg-zinc-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity));
}

.bg-current {
  background-color: currentColor;
}

.bg-opacity-25 {
  --tw-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-\[\#002E50\]\/90 {
  --tw-gradient-from: rgb(0 46 80 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 46 80 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/60 {
  --tw-gradient-from: rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-black\/80 {
  --tw-gradient-from: rgb(0 0 0 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-gray-800 {
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-100\/30 {
  --tw-gradient-from: rgb(219 234 254 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-black\/40 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-black\/70 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.7) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-primary-500 {
  --tw-gradient-to: rgb(59 130 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #3b82f6 var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/80 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.via-white\/90 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.to-\[\#002E50\]\/80 {
  --tw-gradient-to: rgb(0 46 80 / 0.8) var(--tw-gradient-to-position);
}

.to-black\/60 {
  --tw-gradient-to: rgb(0 0 0 / 0.6) var(--tw-gradient-to-position);
}

.to-primary-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.stroke-white\/20 {
  stroke: rgb(255 255 255 / 0.2);
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.object-none {
  -o-object-fit: none;
     object-fit: none;
}

.object-bottom {
  -o-object-position: bottom;
     object-position: bottom;
}

.object-center {
  -o-object-position: center;
     object-position: center;
}

.object-left {
  -o-object-position: left;
     object-position: left;
}

.object-left-top {
  -o-object-position: left top;
     object-position: left top;
}

.object-right {
  -o-object-position: right;
     object-position: right;
}

.object-top {
  -o-object-position: top;
     object-position: top;
}

.p-0 {
  padding: 0px;
}

.p-0\.5 {
  padding: 0.125rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-10 {
  padding: 2.5rem;
}

.p-12 {
  padding: 3rem;
}

.p-16 {
  padding: 4rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-px {
  padding: 1px;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.py-14 {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-24 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-3\.5 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.pb-0\.5 {
  padding-bottom: 0.125rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pb-24 {
  padding-bottom: 6rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pb-9 {
  padding-bottom: 2.25rem;
}

.pl-0 {
  padding-left: 0px;
}

.pl-11 {
  padding-left: 2.75rem;
}

.pl-12 {
  padding-left: 3rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pl-4 {
  padding-left: 1rem;
}

.pl-8 {
  padding-left: 2rem;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pt-1\.5 {
  padding-top: 0.375rem;
}

.pt-10 {
  padding-top: 2.5rem;
}

.pt-12 {
  padding-top: 3rem;
}

.pt-16 {
  padding-top: 4rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-2\.5 {
  padding-top: 0.625rem;
}

.pt-28 {
  padding-top: 7rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-3\.5 {
  padding-top: 0.875rem;
}

.pt-32 {
  padding-top: 8rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pt-56 {
  padding-top: 14rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-64 {
  padding-top: 16rem;
}

.pt-8 {
  padding-top: 2rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-bottom {
  vertical-align: bottom;
}

.font-sans {
  font-family: Inter, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-7xl {
  font-size: 4.5rem;
  line-height: 1;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-xxs {
  font-size: 11px;
  line-height: 14px;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.not-italic {
  font-style: normal;
}

.leading-10 {
  line-height: 2.5rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-8 {
  line-height: 2rem;
}

.leading-none {
  line-height: 1;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-snug {
  line-height: 1.375;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.text-amber-500 {
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.text-gray-50 {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.text-green-200 {
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}

.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}

.text-indigo-200 {
  --tw-text-opacity: 1;
  color: rgb(199 210 254 / var(--tw-text-opacity));
}

.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity));
}

.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity));
}

.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.text-primary-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity));
}

.text-primary-200 {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.text-primary-300 {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity));
}

.text-primary-50 {
  --tw-text-opacity: 1;
  color: rgb(239 246 255 / var(--tw-text-opacity));
}

.text-primary-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.text-primary-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

.text-primary-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity));
}

.text-secondary-300 {
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity));
}

.text-secondary-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}

.text-secondary-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.text-secondary-700 {
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity));
}

.text-secondary-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity));
}

.text-secondary-900 {
  --tw-text-opacity: 1;
  color: rgb(113 63 18 / var(--tw-text-opacity));
}

.text-secondary-950 {
  --tw-text-opacity: 1;
  color: rgb(66 32 6 / var(--tw-text-opacity));
}

.text-sky-500 {
  --tw-text-opacity: 1;
  color: rgb(14 165 233 / var(--tw-text-opacity));
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}

.text-slate-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.text-social-facebook {
  --tw-text-opacity: 1;
  color: rgb(59 89 153 / var(--tw-text-opacity));
}

.text-social-github {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-social-instagram {
  --tw-text-opacity: 1;
  color: rgb(228 64 95 / var(--tw-text-opacity));
}

.text-social-linkedin {
  --tw-text-opacity: 1;
  color: rgb(0 119 181 / var(--tw-text-opacity));
}

.text-social-snapchat {
  --tw-text-opacity: 1;
  color: rgb(255 252 0 / var(--tw-text-opacity));
}

.text-social-tiktok {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-social-twitter {
  --tw-text-opacity: 1;
  color: rgb(85 172 238 / var(--tw-text-opacity));
}

.text-social-whatsapp {
  --tw-text-opacity: 1;
  color: rgb(37 211 102 / var(--tw-text-opacity));
}

.text-social-youtube {
  --tw-text-opacity: 1;
  color: rgb(205 32 31 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-zinc-500 {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity));
}

.text-zinc-700 {
  --tw-text-opacity: 1;
  color: rgb(63 63 70 / var(--tw-text-opacity));
}

.text-zinc-900 {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity));
}

.underline {
  text-decoration-line: underline;
}

.line-through {
  text-decoration-line: line-through;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}

.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-80 {
  opacity: 0.8;
}

.mix-blend-multiply {
  mix-blend-mode: multiply;
}

.mix-blend-soft-light {
  mix-blend-mode: soft-light;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-black\/20 {
  --tw-shadow-color: rgb(0 0 0 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-black\/30 {
  --tw-shadow-color: rgb(0 0 0 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-black\/40 {
  --tw-shadow-color: rgb(0 0 0 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.shadow-primary-500\/30 {
  --tw-shadow-color: rgb(59 130 246 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline {
  outline-style: solid;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-inset {
  --tw-ring-inset: inset;
}

.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity));
}

.ring-blue-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

.ring-gray-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.ring-gray-400\/10 {
  --tw-ring-color: rgb(156 163 175 / 0.1);
}

.ring-gray-900\/5 {
  --tw-ring-color: rgb(17 24 39 / 0.05);
}

.ring-green-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity));
}

.ring-indigo-50 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(238 242 255 / var(--tw-ring-opacity));
}

.ring-orange-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity));
}

.ring-pink-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(236 72 153 / var(--tw-ring-opacity));
}

.ring-purple-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity));
}

.ring-yellow-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity));
}

.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}

.ring-offset-1 {
  --tw-ring-offset-width: 1px;
}

.ring-offset-2 {
  --tw-ring-offset-width: 2px;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.brightness-125 {
  --tw-brightness: brightness(1.25);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.brightness-75 {
  --tw-brightness: brightness(.75);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.saturate-0 {
  --tw-saturate: saturate(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-75 {
  transition-duration: 75ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-linear {
  transition-timing-function: linear;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.oc-boxes-spacing--before-large    [data-rel="boxes-wrapper"] {
  padding-top: 3rem;
}

@media (min-width: 768px) {
  .oc-boxes-spacing--before-large    [data-rel="boxes-wrapper"] {
    padding-top: 4rem;
  }
}

@media (min-width: 1024px) {
  .oc-boxes-spacing--before-large    [data-rel="boxes-wrapper"] {
    padding-top: 6rem;
  }
}

.oc-boxes-spacing--after-large     [data-rel="boxes-wrapper"] {
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .oc-boxes-spacing--after-large     [data-rel="boxes-wrapper"] {
    padding-bottom: 4rem;
  }
}

@media (min-width: 1024px) {
  .oc-boxes-spacing--after-large     [data-rel="boxes-wrapper"] {
    padding-bottom: 6rem;
  }
}

.oc-boxes-spacing--before-medium   [data-rel="boxes-wrapper"] {
  padding-top: 3rem;
}

@media (min-width: 768px) {
  .oc-boxes-spacing--before-medium   [data-rel="boxes-wrapper"] {
    padding-top: 3rem;
  }
}

@media (min-width: 1024px) {
  .oc-boxes-spacing--before-medium   [data-rel="boxes-wrapper"] {
    padding-top: 4rem;
  }
}

.oc-boxes-spacing--after-medium    [data-rel="boxes-wrapper"] {
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .oc-boxes-spacing--after-medium    [data-rel="boxes-wrapper"] {
    padding-bottom: 3rem;
  }
}

@media (min-width: 1024px) {
  .oc-boxes-spacing--after-medium    [data-rel="boxes-wrapper"] {
    padding-bottom: 4rem;
  }
}

.oc-boxes-spacing--before-small    [data-rel="boxes-wrapper"] {
  padding-top: 2.5rem;
}

.oc-boxes-spacing--after-small     [data-rel="boxes-wrapper"] {
  padding-bottom: 2.5rem;
}

.oc-boxes-spacing--before-none     [data-rel="boxes-wrapper"] {
  padding-top: 0px;
}

.oc-boxes-spacing--after-none      [data-rel="boxes-wrapper"] {
  padding-bottom: 0px;
}

.oc-boxes-edit-mode header.sticky {
  position: static;
}

.oc-boxes-edit-mode .bx-da {
  pointer-events: none;
}

.card-same-height .oc-box--child-card,
.card-same-height .oc-box--child-card .card,
.card-same-height .oc-box--child-image,
.card-same-height .oc-box--child-image .image-wrapper {
  height: 100%;
}

.card-same-height .image-wrapper img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

/* Wrapper */

.warmtepompcheck {
  display: flex;
  align-items: center;
}

@media (min-width: 768px) {
  .warmtepompcheck {
    min-height: 600px;
  }
}

/* Fieldset heading */

.warmtepompcheck .fs-heading {
  margin-bottom: 3rem;
  text-align: center;
}

.warmtepompcheck .fs-heading .fs-heading-step {
  order: 1;
  margin-left: 1rem;
  flex-shrink: 0;
}

.warmtepompcheck .fs-heading .fs-heading-step-icon {
  display: flex;
  height: 1.5rem;
  width: 1.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 640px) {
  .warmtepompcheck .fs-heading .fs-heading-step-icon {
    flex-shrink: 0;
  }
}

@media (min-width: 768px) {
  .warmtepompcheck .fs-heading .fs-heading-step-icon {
    height: 2.5rem;
    width: 2.5rem;
  }
}

.warmtepompcheck .fs-heading .fs-heading-step-text {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 700;
}

@media (min-width: 768px) {
  .warmtepompcheck .fs-heading .fs-heading-step-text {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.warmtepompcheck .fs-heading .fs-heading-title {
  order: 3;
  margin-left: 1rem;
  margin-top: 1rem;
  width: 100%;
}

@media (min-width: 768px) {
  .warmtepompcheck .fs-heading .fs-heading-title {
    order: 2;
    margin-top: 0px;
    width: auto;
  }
}

.warmtepompcheck .fs-heading .fs-heading-title-text {
  margin-bottom: 0px;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

/* .warmtepompcheck .fs-heading .fs-heading-title-text { @apply text-lg font-medium leading-none text-white mb-0; } */

.warmtepompcheck .fs-heading .fs-heading-check {
  order: 2;
  margin-left: auto;
  height: 1.5rem;
  font-size: 1.5rem;
  line-height: 2rem;
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .warmtepompcheck .fs-heading .fs-heading-check {
    order: 3;
    height: auto;
  }
}

/* .warmtepompcheck .fs-heading .fs-heading-check { @apply ml-auto order-2 md:order-3 h-6 md:h-auto text-white text-2xl; } */

/* Fieldset body */

.warmtepompcheck .fs-body {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .warmtepompcheck .fs-body {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .warmtepompcheck .fs-body {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Answer grid */

.warmtepompcheck .fs-body .fs-field-grid {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 0.75rem;
}

@media (min-width: 768px) {
  .warmtepompcheck .fs-body .fs-field-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.warmtepompcheck .fs-body .fs-field-grid-md {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 0.75rem;
}

@media (min-width: 768px) {
  .warmtepompcheck .fs-body .fs-field-grid-md {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.warmtepompcheck .fs-body .fs-field-grid-lg {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 0.75rem;
}

@media (min-width: 768px) {
  .warmtepompcheck .fs-body .fs-field-grid-lg {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Field Label */

.warmtepompcheck .fs-body .field-label {
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.warmtepompcheck .fs-body .field-comment {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

/* Field: Radio */

.warmtepompcheck .field-radio {
  position: relative;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  text-align: center;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.warmtepompcheck .field-radio:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.warmtepompcheck .field-radio:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

@media (min-width: 640px) {
  .warmtepompcheck .field-radio {
    flex: 1 1 0%;
  }
}

.warmtepompcheck .field-radio.selected {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

/* Field: Checkbox */

.warmtepompcheck .field-checkbox {
  display: block;
}

.warmtepompcheck .field-checkbox-label {
  position: relative;
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.warmtepompcheck .field-checkbox-label:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.warmtepompcheck .field-checkbox-label:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

@media (min-width: 640px) {
  .warmtepompcheck .field-checkbox-label {
    flex: 1 1 0%;
  }
}

.warmtepompcheck .field-checkbox input:checked ~ .field-checkbox-label {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

.warmtepompcheck .field-checkbox-simple {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.warmtepompcheck .field-checkbox-simple:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

.warmtepompcheck .field-checkbox-simple-label {
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.warmtepompcheck .field-radio .input-check,
.warmtepompcheck .field-checkbox-label .input-check {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.warmtepompcheck .field-radio:hover .input-check,
.warmtepompcheck .field-checkbox-label:hover .input-check {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.warmtepompcheck .field-radio.selected .input-check,
.warmtepompcheck .field-checkbox input:checked ~ .field-checkbox-label .input-check {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

/* Field: Text */

.warmtepompcheck .field-text-label {
  display: block;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.warmtepompcheck .field-text-wrap {
  position: relative;
  margin-top: 0.25rem;
  border-radius: 0.375rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.warmtepompcheck .field-text-leading {
  pointer-events: none;
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0px;
  display: flex;
  align-items: center;
  padding-left: 0.75rem;
}

.warmtepompcheck .field-text-trailing {
  pointer-events: none;
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 0px;
  display: flex;
  align-items: center;
  padding-right: 0.75rem;
}

.warmtepompcheck .field-text-leading-text,
.warmtepompcheck .field-text-trailing-text {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

@media (min-width: 640px) {
  .warmtepompcheck .field-text-leading-text,
.warmtepompcheck .field-text-trailing-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

.warmtepompcheck .field-text-input {
  display: block;
  width: 100%;
  border-radius: 0.375rem;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.warmtepompcheck .field-text-input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

@media (min-width: 640px) {
  .warmtepompcheck .field-text-input {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

.warmtepompcheck .field-text-input.has_leading {
  padding-left: 1.75rem;
}

.warmtepompcheck .field-text-input.has_trailing {
  padding-right: 3rem;
}

/* Buttons */

.warmtepompcheck .fs-buttons {
  margin-top: 3rem;
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: wrap;
  justify-content: center;
}

/* Next button */

.warmtepompcheck .fs-buttons .fs-button-next {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  border-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.warmtepompcheck .fs-buttons .fs-button-next:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.warmtepompcheck .fs-buttons .fs-button-next:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 2px;
}

.warmtepompcheck .fs-buttons .fs-button-next:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

/* Prev button */

.warmtepompcheck .fs-buttons .fs-button-prev {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

/* Submit button */

.warmtepompcheck .fs-buttons .fs-button-submit {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  border-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity));
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.warmtepompcheck .fs-buttons .fs-button-submit:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity));
}

.warmtepompcheck .fs-buttons .fs-button-submit:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 2px;
}

.warmtepompcheck .fs-buttons .fs-button-submit:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

/* Fieldset steps */

.warmtepompcheck .fs-steps-list {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.warmtepompcheck .fs-steps-list > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.warmtepompcheck .fs-steps-list .fs-step {
  height: 0.5rem;
  width: 2rem;
  border-radius: 9999px;
}

/* Step: Current */

.warmtepompcheck .fs-steps-list .fs-step-current {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

/* Step: Completed */

.warmtepompcheck .fs-steps-list .fs-step-completed {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

/* Step: Upcoming */

.warmtepompcheck .fs-steps-list .fs-step-upcoming {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.oc-img-full {
  width: 100% !important;
}

.oc-img-circle {
  border-radius: 9999px;
}

.oc-img-rounded {
  border-radius: 0.5rem;
}

/* Text */

.oc-text-gray {
  color: #AAA !important;
}

.oc-text-bordered {
  border-top: solid 1px #222;
  border-bottom: solid 1px #222;
  padding: 10px 0;
}

.oc-text-spaced {
  letter-spacing: 1px;
}

.oc-text-uppercase {
  text-transform: uppercase;
}

/* Links */

a.oc-link-strong {
  font-weight: 700;
}

a.oc-link-green {
  color: green;
}

/* Table */

table.oc-dashed-borders td,
table.oc-dashed-borders th {
  border-style: dashed;
}

table.oc-alternate-rows tbody tr:nth-child(2n) {
  background: #f5f5f5;
}

/* Table cell */

table td.oc-cell-highlighted,
table th.oc-cell-highlighted {
  border: 1px double red;
}

table td.oc-cell-thick-border,
table th.oc-cell-thick-border {
  border-width: 2px;
}

/* Images */

img.oc-img-circle {
  border-radius: 100%;
  background-clip: padding-box;
}

img.oc-img-rounded {
  border-radius: 0.5rem;
}

img.oc-img-bordered {
  border: solid 10px #CCC;
  box-sizing: content-box;
}

img.oc-img-full {
  width: 100% !important;
}

img.img-fill-box {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  margin: 0 !important;
}

[x-cloak] {
  display: none !important;
}

.flexcenter {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: 768px) {
  .md\:aspect-h-3 {
    --tw-aspect-h: 3;
  }

  .md\:aspect-h-9 {
    --tw-aspect-h: 9;
  }

  .md\:aspect-w-16 {
    position: relative;
    padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
    --tw-aspect-w: 16;
  }

  .md\:aspect-w-16 > * {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  .md\:aspect-w-4 {
    position: relative;
    padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
    --tw-aspect-w: 4;
  }

  .md\:aspect-w-4 > * {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }
}

@media (min-width: 1024px) {
  .lg\:container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 1rem;
    padding-left: 1rem;
  }

  @media (min-width: 425px) {
    .lg\:container {
      max-width: 425px;
    }
  }

  @media (min-width: 640px) {
    .lg\:container {
      max-width: 640px;
    }
  }

  @media (min-width: 768px) {
    .lg\:container {
      max-width: 768px;
    }
  }

  @media (min-width: 1024px) {
    .lg\:container {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {
    .lg\:container {
      max-width: 1280px;
    }
  }

  @media (min-width: 1536px) {
    .lg\:container {
      max-width: 1536px;
    }
  }

  .lg\:prose-lg {
    font-size: 1.125rem;
    line-height: 1.7777778;
  }

  .lg\:prose-lg :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.2222222em;
    line-height: 1.4545455;
    margin-top: 1.0909091em;
    margin-bottom: 1.0909091em;
  }

  .lg\:prose-lg :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.6666667em;
    margin-bottom: 1.6666667em;
    padding-inline-start: 1em;
  }

  .lg\:prose-lg :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 2.6666667em;
    margin-top: 0;
    margin-bottom: 0.8333333em;
    line-height: 1;
  }

  .lg\:prose-lg :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.6666667em;
    margin-top: 1.8666667em;
    margin-bottom: 1.0666667em;
    line-height: 1.3333333;
  }

  .lg\:prose-lg :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 1.3333333em;
    margin-top: 1.6666667em;
    margin-bottom: 0.6666667em;
    line-height: 1.5;
  }

  .lg\:prose-lg :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 0.4444444em;
    line-height: 1.5555556;
  }

  .lg\:prose-lg :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .lg\:prose-lg :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    border-radius: 0.3125rem;
    padding-top: 0.2222222em;
    padding-inline-end: 0.4444444em;
    padding-bottom: 0.2222222em;
    padding-inline-start: 0.4444444em;
  }

  .lg\:prose-lg :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
  }

  .lg\:prose-lg :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8666667em;
  }

  .lg\:prose-lg :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.875em;
  }

  .lg\:prose-lg :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.75;
    margin-top: 2em;
    margin-bottom: 2em;
    border-radius: 0.375rem;
    padding-top: 1em;
    padding-inline-end: 1.5em;
    padding-bottom: 1em;
    padding-inline-start: 1.5em;
  }

  .lg\:prose-lg :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .lg\:prose-lg :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
    padding-inline-start: 1.5555556em;
  }

  .lg\:prose-lg :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    margin-bottom: 0.6666667em;
  }

  .lg\:prose-lg :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .lg\:prose-lg :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0.4444444em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.8888889em;
    margin-bottom: 0.8888889em;
  }

  .lg\:prose-lg :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
    margin-bottom: 1.3333333em;
  }

  .lg\:prose-lg :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.3333333em;
  }

  .lg\:prose-lg :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0.6666667em;
    padding-inline-start: 1.5555556em;
  }

  .lg\:prose-lg :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 3.1111111em;
    margin-bottom: 3.1111111em;
  }

  .lg\:prose-lg :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
  }

  .lg\:prose-lg :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .lg\:prose-lg :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .lg\:prose-lg :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .lg\:prose-lg :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-top: 0.75em;
    padding-inline-end: 0.75em;
    padding-bottom: 0.75em;
    padding-inline-start: 0.75em;
  }

  .lg\:prose-lg :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .lg\:prose-lg :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .lg\:prose-lg :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 1.7777778em;
    margin-bottom: 1.7777778em;
  }

  .lg\:prose-lg :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .lg\:prose-lg :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    font-size: 0.8888889em;
    line-height: 1.5;
    margin-top: 1em;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-top: 0;
  }

  .lg\:prose-lg :where(.lg\:prose-lg > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
    margin-bottom: 0;
  }
}

.dark\:prose-primary_inverted:where(.dark, .dark *) {
  --tw-prose-body: #f3f4f6;
  --tw-prose-headings: #f3f4f6;
  --tw-prose-lead: #fff;
  --tw-prose-links: #fff;
  --tw-prose-bold: #fff;
  --tw-prose-counters: #fff;
  --tw-prose-bullets: #fff;
  --tw-prose-hr: #1d4ed8;
  --tw-prose-quotes: #fff;
  --tw-prose-quote-borders: #2563eb;
  --tw-prose-captions: #9ca3af;
  --tw-prose-code: #fff;
  --tw-prose-pre-code: #93c5fd;
  --tw-prose-pre-bg: rgb(0 0 0 / 25%);
  --tw-prose-th-borders: #e5e7eb;
  --tw-prose-td-borders: #e5e7eb;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h1 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h2 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h3 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h4 b):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #2563eb;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #f3f4f6;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #f3f4f6;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #dbeafe;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
  color: #dbeafe;
}

.dark\:prose-primary_inverted:where(.dark, .dark *) :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)):hover {
  color: #2563eb;
}

.placeholder\:text-gray-400::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.placeholder\:text-gray-400::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.before\:mr-1::before {
  content: var(--tw-content);
  margin-right: 0.25rem;
}

.before\:content-\[\'\20AC\'\]::before {
  --tw-content: '€';
  content: var(--tw-content);
}

.after\:hidden::after {
  content: var(--tw-content);
  display: none;
}

.last-of-type\:border-b-0:last-of-type {
  border-bottom-width: 0px;
}

.focus-within\:outline-none:focus-within {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-within\:ring-2:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-primary-600:focus-within {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity));
}

.focus-within\:ring-offset-2:focus-within {
  --tw-ring-offset-width: 2px;
}

.hover\:border-2:hover {
  border-width: 2px;
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.hover\:border-gray-900\/50:hover {
  border-color: rgb(17 24 39 / 0.5);
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.hover\:bg-green-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity));
}

.hover\:bg-green-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity));
}

.hover\:bg-orange-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity));
}

.hover\:bg-orange-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity));
}

.hover\:bg-primary-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity));
}

.hover\:bg-primary-300:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity));
}

.hover\:bg-primary-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.hover\:bg-primary-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.hover\:bg-secondary-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity));
}

.hover\:bg-secondary-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity));
}

.hover\:bg-sky-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(2 132 199 / var(--tw-bg-opacity));
}

.hover\:text-black:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.hover\:text-orange-500:hover {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.hover\:text-primary-200:hover {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.hover\:text-primary-400:hover {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.hover\:text-primary-500:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.hover\:text-primary-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.hover\:text-primary-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

.hover\:text-secondary-500:hover {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.hover\:text-secondary-600:hover {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity));
}

.hover\:text-sky-600:hover {
  --tw-text-opacity: 1;
  color: rgb(2 132 199 / var(--tw-text-opacity));
}

.hover\:text-social-facebook:hover {
  --tw-text-opacity: 1;
  color: rgb(59 89 153 / var(--tw-text-opacity));
}

.hover\:text-social-github:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-social-instagram:hover {
  --tw-text-opacity: 1;
  color: rgb(228 64 95 / var(--tw-text-opacity));
}

.hover\:text-social-linkedin:hover {
  --tw-text-opacity: 1;
  color: rgb(0 119 181 / var(--tw-text-opacity));
}

.hover\:text-social-snapchat:hover {
  --tw-text-opacity: 1;
  color: rgb(255 252 0 / var(--tw-text-opacity));
}

.hover\:text-social-tiktok:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-social-x:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-social-youtube:hover {
  --tw-text-opacity: 1;
  color: rgb(205 32 31 / var(--tw-text-opacity));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-black\/40:hover {
  --tw-shadow-color: rgb(0 0 0 / 0.4);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:grayscale-0:hover {
  --tw-grayscale: grayscale(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.focus\:z-20:focus {
  z-index: 20;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:outline-offset-0:focus {
  outline-offset: 0px;
}

.focus\:ring:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-gray-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity));
}

.focus\:ring-orange-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity));
}

.focus\:ring-primary-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity));
}

.focus\:ring-primary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

.focus\:ring-primary-600:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity));
}

.focus\:ring-secondary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity));
}

.focus\:ring-white:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus\:ring-offset-gray-50:focus {
  --tw-ring-offset-color: #f9fafb;
}

.focus-visible\:outline:focus-visible {
  outline-style: solid;
}

.focus-visible\:outline-2:focus-visible {
  outline-width: 2px;
}

.focus-visible\:outline-offset-2:focus-visible {
  outline-offset: 2px;
}

.focus-visible\:outline-offset-\[-4px\]:focus-visible {
  outline-offset: -4px;
}

.focus-visible\:outline-indigo-600:focus-visible {
  outline-color: #4f46e5;
}

.disabled\:bg-gray-400:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:block {
  display: block;
}

.group\/menuitem:hover .group-hover\/menuitem\:flex {
  display: flex;
}

.group\/card:hover .group-hover\/card\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-\[1\.15\] {
  --tw-scale-x: 1.15;
  --tw-scale-y: 1.15;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes ring {
  0%, 100% {
    transform: rotate(-6deg);
  }

  50% {
    transform: rotate(6deg);
  }
}

.group:hover .group-hover\:animate-ring {
  animation: ring 1s ease-in-out infinite;
}

.group\/card:hover .group-hover\/card\:bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.group\/card:hover .group-hover\/card\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.group:hover .group-hover\:opacity-75 {
  opacity: 0.75;
}

.group:hover .group-hover\:opacity-80 {
  opacity: 0.8;
}

.peer:checked ~ .peer-checked\:bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.peer:checked ~ .peer-checked\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.peer:hover ~ .peer-hover\:grayscale-0 {
  --tw-grayscale: grayscale(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.prose-h3\:text-gray-700 :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.prose-p\:text-gray-500 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.prose-p\:text-gray-600 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.prose-p\:text-gray-900 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.prose-a\:no-underline :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  text-decoration-line: none;
}

.hover\:prose-a\:underline :is(:where(a):not(:where([class~="not-prose"],[class~="not-prose"] *))):hover {
  text-decoration-line: underline;
}

.prose-ul\:pl-2 :is(:where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *))) {
  padding-left: 0.5rem;
}

@media (min-width: 425px) {
  .xs\:ml-0 {
    margin-left: 0px;
  }

  .xs\:block {
    display: block;
  }
}

@media (min-width: 640px) {
  .sm\:order-2 {
    order: 2;
  }

  .sm\:order-3 {
    order: 3;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:-mx-6 {
    margin-left: -1.5rem;
    margin-right: -1.5rem;
  }

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .sm\:-mr-80 {
    margin-right: -20rem;
  }

  .sm\:ml-2 {
    margin-left: 0.5rem;
  }

  .sm\:ml-4 {
    margin-left: 1rem;
  }

  .sm\:ml-6 {
    margin-left: 1.5rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-16 {
    margin-top: 4rem;
  }

  .sm\:mt-20 {
    margin-top: 5rem;
  }

  .sm\:mt-4 {
    margin-top: 1rem;
  }

  .sm\:mt-5 {
    margin-top: 1.25rem;
  }

  .sm\:mt-6 {
    margin-top: 1.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline-block {
    display: inline-block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:grid {
    display: grid;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:aspect-auto {
    aspect-ratio: auto;
  }

  .sm\:h-10 {
    height: 2.5rem;
  }

  .sm\:h-2\/3 {
    height: 66.666667%;
  }

  .sm\:h-32 {
    height: 8rem;
  }

  .sm\:h-72 {
    height: 18rem;
  }

  .sm\:h-full {
    height: 100%;
  }

  .sm\:h-screen {
    height: 100vh;
  }

  .sm\:w-10 {
    width: 2.5rem;
  }

  .sm\:w-\[57rem\] {
    width: 57rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:min-w-0 {
    min-width: 0px;
  }

  .sm\:max-w-3xl {
    max-width: 48rem;
  }

  .sm\:max-w-4xl {
    max-width: 56rem;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-xl {
    max-width: 36rem;
  }

  .sm\:flex-1 {
    flex: 1 1 0%;
  }

  .sm\:shrink-0 {
    flex-shrink: 0;
  }

  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-center {
    justify-content: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:gap-x-6 {
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
  }

  .sm\:gap-y-10 {
    row-gap: 2.5rem;
  }

  .sm\:gap-y-20 {
    row-gap: 5rem;
  }

  .sm\:gap-y-24 {
    row-gap: 6rem;
  }

  .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .sm\:rounded-md {
    border-radius: 0.375rem;
  }

  .sm\:p-0 {
    padding: 0px;
  }

  .sm\:p-10 {
    padding: 2.5rem;
  }

  .sm\:p-3 {
    padding: 0.75rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .sm\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .sm\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .sm\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .sm\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .sm\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .sm\:pb-12 {
    padding-bottom: 3rem;
  }

  .sm\:pb-32 {
    padding-bottom: 8rem;
  }

  .sm\:pb-5 {
    padding-bottom: 1.25rem;
  }

  .sm\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .sm\:pl-0 {
    padding-left: 0px;
  }

  .sm\:pl-6 {
    padding-left: 1.5rem;
  }

  .sm\:pr-0 {
    padding-right: 0px;
  }

  .sm\:pr-6 {
    padding-right: 1.5rem;
  }

  .sm\:pt-12 {
    padding-top: 3rem;
  }

  .sm\:pt-16 {
    padding-top: 4rem;
  }

  .sm\:pt-24 {
    padding-top: 6rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-center {
    text-align: center;
  }

  .sm\:align-middle {
    vertical-align: middle;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:tracking-tight {
    letter-spacing: -0.025em;
  }
}

@media (min-width: 768px) {
  .md\:absolute {
    position: absolute;
  }

  .md\:inset-0 {
    inset: 0px;
  }

  .md\:-bottom-8 {
    bottom: -2rem;
  }

  .md\:bottom-10 {
    bottom: 2.5rem;
  }

  .md\:bottom-auto {
    bottom: auto;
  }

  .md\:left-0 {
    left: 0px;
  }

  .md\:left-auto {
    left: auto;
  }

  .md\:right-0 {
    right: 0px;
  }

  .md\:right-1\/2 {
    right: 50%;
  }

  .md\:top-1\/2 {
    top: 50%;
  }

  .md\:z-20 {
    z-index: 20;
  }

  .md\:order-1 {
    order: 1;
  }

  .md\:order-2 {
    order: 2;
  }

  .md\:order-3 {
    order: 3;
  }

  .md\:order-4 {
    order: 4;
  }

  .md\:order-first {
    order: -9999;
  }

  .md\:order-last {
    order: 9999;
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .md\:col-span-11 {
    grid-column: span 11 / span 11;
  }

  .md\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .md\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .md\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .md\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .md\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .md\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .md\:col-start-2 {
    grid-column-start: 2;
  }

  .md\:col-start-3 {
    grid-column-start: 3;
  }

  .md\:col-start-6 {
    grid-column-start: 6;
  }

  .md\:row-span-1 {
    grid-row: span 1 / span 1;
  }

  .md\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .md\:row-span-3 {
    grid-row: span 3 / span 3;
  }

  .md\:row-span-4 {
    grid-row: span 4 / span 4;
  }

  .md\:-mx-4 {
    margin-left: -1rem;
    margin-right: -1rem;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:mx-20 {
    margin-left: 5rem;
    margin-right: 5rem;
  }

  .md\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .md\:my-3 {
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .md\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .md\:-mb-12 {
    margin-bottom: -3rem;
  }

  .md\:-mb-16 {
    margin-bottom: -4rem;
  }

  .md\:-ml-4 {
    margin-left: -1rem;
  }

  .md\:-mr-16 {
    margin-right: -4rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-12 {
    margin-bottom: 3rem;
  }

  .md\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .md\:ml-auto {
    margin-left: auto;
  }

  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:mr-2 {
    margin-right: 0.5rem;
  }

  .md\:mr-3 {
    margin-right: 0.75rem;
  }

  .md\:mr-auto {
    margin-right: auto;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mt-12 {
    margin-top: 3rem;
  }

  .md\:mt-14 {
    margin-top: 3.5rem;
  }

  .md\:mt-16 {
    margin-top: 4rem;
  }

  .md\:mt-32 {
    margin-top: 8rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:mt-5 {
    margin-top: 1.25rem;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:inline-flex {
    display: inline-flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:aspect-auto {
    aspect-ratio: auto;
  }

  .md\:aspect-square {
    aspect-ratio: 1 / 1;
  }

  .md\:aspect-thumb {
    aspect-ratio: 4 / 3;
  }

  .md\:aspect-video {
    aspect-ratio: 16 / 9;
  }

  .md\:h-96 {
    height: 24rem;
  }

  .md\:h-\[180px\] {
    height: 180px;
  }

  .md\:h-\[360px\] {
    height: 360px;
  }

  .md\:h-\[480px\] {
    height: 480px;
  }

  .md\:h-\[600px\] {
    height: 600px;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:h-px {
    height: 1px;
  }

  .md\:max-h-\[440px\] {
    max-height: 440px;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-2\/3 {
    width: 66.666667%;
  }

  .md\:w-3\/12 {
    width: 25%;
  }

  .md\:w-4\/6 {
    width: 66.666667%;
  }

  .md\:w-5\/12 {
    width: 41.666667%;
  }

  .md\:w-\[40\%\] {
    width: 40%;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:max-w-2xl {
    max-width: 42rem;
  }

  .md\:max-w-4xl {
    max-width: 56rem;
  }

  .md\:max-w-5xl {
    max-width: 64rem;
  }

  .md\:max-w-none {
    max-width: none;
  }

  .md\:flex-1 {
    flex: 1 1 0%;
  }

  .md\:shrink-0 {
    flex-shrink: 0;
  }

  .md\:-translate-y-1\/2 {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:translate-y-6 {
    --tw-translate-y: 1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-10 {
    grid-template-columns: repeat(10, minmax(0, 1fr));
  }

  .md\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:flex-wrap {
    flex-wrap: wrap;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-normal {
    justify-content: normal;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:justify-evenly {
    justify-content: space-evenly;
  }

  .md\:gap-0 {
    gap: 0px;
  }

  .md\:gap-12 {
    gap: 3rem;
  }

  .md\:gap-16 {
    gap: 4rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-8 {
    gap: 2rem;
  }

  .md\:gap-x-0 {
    -moz-column-gap: 0px;
         column-gap: 0px;
  }

  .md\:gap-x-12 {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }

  .md\:gap-x-16 {
    -moz-column-gap: 4rem;
         column-gap: 4rem;
  }

  .md\:gap-x-32 {
    -moz-column-gap: 8rem;
         column-gap: 8rem;
  }

  .md\:gap-x-4 {
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }

  .md\:gap-x-8 {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }

  .md\:gap-y-0 {
    row-gap: 0px;
  }

  .md\:gap-y-12 {
    row-gap: 3rem;
  }

  .md\:gap-y-16 {
    row-gap: 4rem;
  }

  .md\:gap-y-32 {
    row-gap: 8rem;
  }

  .md\:gap-y-4 {
    row-gap: 1rem;
  }

  .md\:gap-y-8 {
    row-gap: 2rem;
  }

  .md\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:space-y-12 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }

  .md\:space-y-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
  }

  .md\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .md\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }

  .md\:divide-none > :not([hidden]) ~ :not([hidden]) {
    border-style: none;
  }

  .md\:rounded-lg {
    border-radius: 0.5rem;
  }

  .md\:rounded-l {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }

  .md\:rounded-r {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }

  .md\:border-y-0 {
    border-top-width: 0px;
    border-bottom-width: 0px;
  }

  .md\:border-b {
    border-bottom-width: 1px;
  }

  .md\:border-b-0 {
    border-bottom-width: 0px;
  }

  .md\:border-l {
    border-left-width: 1px;
  }

  .md\:border-l-2 {
    border-left-width: 2px;
  }

  .md\:border-r {
    border-right-width: 1px;
  }

  .md\:border-t {
    border-top-width: 1px;
  }

  .md\:border-t-0 {
    border-top-width: 0px;
  }

  .md\:border-none {
    border-style: none;
  }

  .md\:border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity));
  }

  .md\:border-gray-400 {
    --tw-border-opacity: 1;
    border-color: rgb(156 163 175 / var(--tw-border-opacity));
  }

  .md\:object-cover {
    -o-object-fit: cover;
       object-fit: cover;
  }

  .md\:p-0 {
    padding: 0px;
  }

  .md\:p-10 {
    padding: 2.5rem;
  }

  .md\:p-20 {
    padding: 5rem;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .md\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .md\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .md\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pb-12 {
    padding-bottom: 3rem;
  }

  .md\:pb-16 {
    padding-bottom: 4rem;
  }

  .md\:pb-4 {
    padding-bottom: 1rem;
  }

  .md\:pb-8 {
    padding-bottom: 2rem;
  }

  .md\:pl-0 {
    padding-left: 0px;
  }

  .md\:pl-12 {
    padding-left: 3rem;
  }

  .md\:pr-12 {
    padding-right: 3rem;
  }

  .md\:pr-3 {
    padding-right: 0.75rem;
  }

  .md\:pr-8 {
    padding-right: 2rem;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:pt-10 {
    padding-top: 2.5rem;
  }

  .md\:pt-12 {
    padding-top: 3rem;
  }

  .md\:pt-16 {
    padding-top: 4rem;
  }

  .md\:pt-2 {
    padding-top: 0.5rem;
  }

  .md\:pt-20 {
    padding-top: 5rem;
  }

  .md\:pt-24 {
    padding-top: 6rem;
  }

  .md\:pt-3 {
    padding-top: 0.75rem;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-center {
    text-align: center;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}

@media (min-width: 1024px) {
  .lg\:static {
    position: static;
  }

  .lg\:absolute {
    position: absolute;
  }

  .lg\:relative {
    position: relative;
  }

  .lg\:sticky {
    position: sticky;
  }

  .lg\:inset-0 {
    inset: 0px;
  }

  .lg\:inset-y-0 {
    top: 0px;
    bottom: 0px;
  }

  .lg\:bottom-\[68px\] {
    bottom: 68px;
  }

  .lg\:left-0 {
    left: 0px;
  }

  .lg\:left-auto {
    left: auto;
  }

  .lg\:right-0 {
    right: 0px;
  }

  .lg\:right-1\/2 {
    right: 50%;
  }

  .lg\:top-1\/2 {
    top: 50%;
  }

  .lg\:top-4 {
    top: 1rem;
  }

  .lg\:top-auto {
    top: auto;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:order-3 {
    order: 3;
  }

  .lg\:order-first {
    order: -9999;
  }

  .lg\:order-last {
    order: 9999;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .lg\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .lg\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .lg\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .lg\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .lg\:col-start-1 {
    grid-column-start: 1;
  }

  .lg\:col-start-2 {
    grid-column-start: 2;
  }

  .lg\:col-start-3 {
    grid-column-start: 3;
  }

  .lg\:col-start-8 {
    grid-column-start: 8;
  }

  .lg\:col-start-9 {
    grid-column-start: 9;
  }

  .lg\:row-span-1 {
    grid-row: span 1 / span 1;
  }

  .lg\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .lg\:row-start-1 {
    grid-row-start: 1;
  }

  .lg\:row-start-2 {
    grid-row-start: 2;
  }

  .lg\:row-end-1 {
    grid-row-end: 1;
  }

  .lg\:row-end-2 {
    grid-row-end: 2;
  }

  .lg\:m-0 {
    margin: 0px;
  }

  .lg\:-mx-8 {
    margin-left: -2rem;
    margin-right: -2rem;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:mx-52 {
    margin-left: 13rem;
    margin-right: 13rem;
  }

  .lg\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .lg\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .lg\:my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .lg\:-mb-16 {
    margin-bottom: -4rem;
  }

  .lg\:-mb-24 {
    margin-bottom: -6rem;
  }

  .lg\:-ml-0 {
    margin-left: -0px;
  }

  .lg\:-mr-8 {
    margin-right: -2rem;
  }

  .lg\:-mr-96 {
    margin-right: -24rem;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mb-12 {
    margin-bottom: 3rem;
  }

  .lg\:mb-16 {
    margin-bottom: 4rem;
  }

  .lg\:mb-20 {
    margin-bottom: 5rem;
  }

  .lg\:mb-4 {
    margin-bottom: 1rem;
  }

  .lg\:mb-56 {
    margin-bottom: 14rem;
  }

  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .lg\:mb-8 {
    margin-bottom: 2rem;
  }

  .lg\:ml-0 {
    margin-left: 0px;
  }

  .lg\:ml-6 {
    margin-left: 1.5rem;
  }

  .lg\:ml-auto {
    margin-left: auto;
  }

  .lg\:mr-0 {
    margin-right: 0px;
  }

  .lg\:mr-auto {
    margin-right: auto;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:mt-12 {
    margin-top: 3rem;
  }

  .lg\:mt-24 {
    margin-top: 6rem;
  }

  .lg\:mt-8 {
    margin-top: 2rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:aspect-\[3\/2\] {
    aspect-ratio: 3/2;
  }

  .lg\:aspect-\[5\/2\] {
    aspect-ratio: 5/2;
  }

  .lg\:aspect-\[8\/3\] {
    aspect-ratio: 8/3;
  }

  .lg\:aspect-auto {
    aspect-ratio: auto;
  }

  .lg\:aspect-square {
    aspect-ratio: 1 / 1;
  }

  .lg\:h-14 {
    height: 3.5rem;
  }

  .lg\:h-32 {
    height: 8rem;
  }

  .lg\:h-6 {
    height: 1.5rem;
  }

  .lg\:h-\[228px\] {
    height: 228px;
  }

  .lg\:h-\[720px\] {
    height: 720px;
  }

  .lg\:h-auto {
    height: auto;
  }

  .lg\:h-full {
    height: 100%;
  }

  .lg\:h-px {
    height: 1px;
  }

  .lg\:h-screen {
    height: 100vh;
  }

  .lg\:max-h-\[500px\] {
    max-height: 500px;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-1\/4 {
    width: 25%;
  }

  .lg\:w-1\/5 {
    width: 20%;
  }

  .lg\:w-12 {
    width: 3rem;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:w-32 {
    width: 8rem;
  }

  .lg\:w-4\/5 {
    width: 80%;
  }

  .lg\:w-5\/12 {
    width: 41.666667%;
  }

  .lg\:w-6 {
    width: 1.5rem;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:w-px {
    width: 1px;
  }

  .lg\:max-w-2xl {
    max-width: 42rem;
  }

  .lg\:max-w-5xl {
    max-width: 64rem;
  }

  .lg\:max-w-7xl {
    max-width: 80rem;
  }

  .lg\:max-w-\[75\%\] {
    max-width: 75%;
  }

  .lg\:max-w-lg {
    max-width: 32rem;
  }

  .lg\:max-w-none {
    max-width: none;
  }

  .lg\:flex-1 {
    flex: 1 1 0%;
  }

  .lg\:translate-x-24 {
    --tw-translate-x: 6rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-12 {
    --tw-translate-y: 3rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:translate-y-6 {
    --tw-translate-y: 1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:scale-0 {
    --tw-scale-x: 0;
    --tw-scale-y: 0;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-flow-row-dense {
    grid-auto-flow: row dense;
  }

  .lg\:grid-flow-col-dense {
    grid-auto-flow: column dense;
  }

  .lg\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .lg\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .lg\:grid-rows-1 {
    grid-template-rows: repeat(1, minmax(0, 1fr));
  }

  .lg\:grid-rows-2 {
    grid-template-rows: repeat(2, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:flex-wrap {
    flex-wrap: wrap;
  }

  .lg\:items-start {
    align-items: flex-start;
  }

  .lg\:items-end {
    align-items: flex-end;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-normal {
    justify-content: normal;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:justify-center {
    justify-content: center;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-10 {
    gap: 2.5rem;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-14 {
    gap: 3.5rem;
  }

  .lg\:gap-16 {
    gap: 4rem;
  }

  .lg\:gap-20 {
    gap: 5rem;
  }

  .lg\:gap-24 {
    gap: 6rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:gap-x-0\.5 {
    -moz-column-gap: 0.125rem;
         column-gap: 0.125rem;
  }

  .lg\:gap-x-12 {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }

  .lg\:gap-x-16 {
    -moz-column-gap: 4rem;
         column-gap: 4rem;
  }

  .lg\:gap-x-3 {
    -moz-column-gap: 0.75rem;
         column-gap: 0.75rem;
  }

  .lg\:gap-x-8 {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }

  .lg\:gap-y-10 {
    row-gap: 2.5rem;
  }

  .lg\:gap-y-12 {
    row-gap: 3rem;
  }

  .lg\:gap-y-6 {
    row-gap: 1.5rem;
  }

  .lg\:gap-y-8 {
    row-gap: 2rem;
  }

  .lg\:space-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0px * var(--tw-space-x-reverse));
    margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.25rem * var(--tw-space-x-reverse));
    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .lg\:space-y-12 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }

  .lg\:space-y-16 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
  }

  .lg\:space-y-20 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(5rem * var(--tw-space-y-reverse));
  }

  .lg\:space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
  }

  .lg\:overflow-hidden {
    overflow: hidden;
  }

  .lg\:overflow-visible {
    overflow: visible;
  }

  .lg\:rounded {
    border-radius: 0.25rem;
  }

  .lg\:rounded-l {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }

  .lg\:rounded-r {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }

  .lg\:border-x {
    border-left-width: 1px;
    border-right-width: 1px;
  }

  .lg\:border-b {
    border-bottom-width: 1px;
  }

  .lg\:border-b-0 {
    border-bottom-width: 0px;
  }

  .lg\:border-l {
    border-left-width: 1px;
  }

  .lg\:border-r {
    border-right-width: 1px;
  }

  .lg\:border-r-0 {
    border-right-width: 0px;
  }

  .lg\:border-t {
    border-top-width: 1px;
  }

  .lg\:border-secondary-100 {
    --tw-border-opacity: 1;
    border-color: rgb(254 249 195 / var(--tw-border-opacity));
  }

  .lg\:object-contain {
    -o-object-fit: contain;
       object-fit: contain;
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:p-10 {
    padding: 2.5rem;
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:p-16 {
    padding: 4rem;
  }

  .lg\:p-4 {
    padding: 1rem;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .lg\:px-24 {
    padding-left: 6rem;
    padding-right: 6rem;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:px-px {
    padding-left: 1px;
    padding-right: 1px;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .lg\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .lg\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .lg\:py-28 {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .lg\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .lg\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .lg\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .lg\:pb-0 {
    padding-bottom: 0px;
  }

  .lg\:pb-12 {
    padding-bottom: 3rem;
  }

  .lg\:pb-16 {
    padding-bottom: 4rem;
  }

  .lg\:pb-28 {
    padding-bottom: 7rem;
  }

  .lg\:pb-4 {
    padding-bottom: 1rem;
  }

  .lg\:pb-56 {
    padding-bottom: 14rem;
  }

  .lg\:pb-8 {
    padding-bottom: 2rem;
  }

  .lg\:pl-0 {
    padding-left: 0px;
  }

  .lg\:pl-14 {
    padding-left: 3.5rem;
  }

  .lg\:pl-16 {
    padding-left: 4rem;
  }

  .lg\:pl-4 {
    padding-left: 1rem;
  }

  .lg\:pl-8 {
    padding-left: 2rem;
  }

  .lg\:pr-0 {
    padding-right: 0px;
  }

  .lg\:pr-16 {
    padding-right: 4rem;
  }

  .lg\:pr-3 {
    padding-right: 0.75rem;
  }

  .lg\:pr-4 {
    padding-right: 1rem;
  }

  .lg\:pr-8 {
    padding-right: 2rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:pt-14 {
    padding-top: 3.5rem;
  }

  .lg\:pt-16 {
    padding-top: 4rem;
  }

  .lg\:pt-2 {
    padding-top: 0.5rem;
  }

  .lg\:pt-20 {
    padding-top: 5rem;
  }

  .lg\:pt-24 {
    padding-top: 6rem;
  }

  .lg\:pt-4 {
    padding-top: 1rem;
  }

  .lg\:pt-40 {
    padding-top: 10rem;
  }

  .lg\:pt-48 {
    padding-top: 12rem;
  }

  .lg\:pt-6 {
    padding-top: 1.5rem;
  }

  .lg\:pt-8 {
    padding-top: 2rem;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-center {
    text-align: center;
  }

  .lg\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:leading-snug {
    line-height: 1.375;
  }

  .lg\:text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity));
  }

  .lg\:text-secondary-500 {
    --tw-text-opacity: 1;
    color: rgb(234 179 8 / var(--tw-text-opacity));
  }

  .lg\:opacity-0 {
    opacity: 0;
  }

  .lg\:hover\:-translate-y-6:hover {
    --tw-translate-y: -1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:hover\:translate-y-0:hover {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:hover\:translate-y-6:hover {
    --tw-translate-y: 1.5rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:hover\:border-primary-500:hover {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246 / var(--tw-border-opacity));
  }

  .group:hover .lg\:group-hover\:bottom-0 {
    bottom: 0px;
  }

  .group:hover .lg\:group-hover\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .group:hover .lg\:group-hover\:opacity-100 {
    opacity: 1;
  }
}

@media (min-width: 1280px) {
  .xl\:static {
    position: static;
  }

  .xl\:absolute {
    position: absolute;
  }

  .xl\:sticky {
    position: sticky;
  }

  .xl\:inset-0 {
    inset: 0px;
  }

  .xl\:left-1\/2 {
    left: 50%;
  }

  .xl\:top-8 {
    top: 2rem;
  }

  .xl\:z-10 {
    z-index: 10;
  }

  .xl\:order-1 {
    order: 1;
  }

  .xl\:order-first {
    order: -9999;
  }

  .xl\:order-last {
    order: 9999;
  }

  .xl\:col-auto {
    grid-column: auto;
  }

  .xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .xl\:col-span-10 {
    grid-column: span 10 / span 10;
  }

  .xl\:col-span-11 {
    grid-column: span 11 / span 11;
  }

  .xl\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .xl\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .xl\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .xl\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .xl\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .xl\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .xl\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .xl\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .xl\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .xl\:col-start-1 {
    grid-column-start: 1;
  }

  .xl\:col-start-2 {
    grid-column-start: 2;
  }

  .xl\:col-start-4 {
    grid-column-start: 4;
  }

  .xl\:col-start-7 {
    grid-column-start: 7;
  }

  .xl\:col-start-8 {
    grid-column-start: 8;
  }

  .xl\:col-end-1 {
    grid-column-end: 1;
  }

  .xl\:row-span-1 {
    grid-row: span 1 / span 1;
  }

  .xl\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .xl\:row-span-3 {
    grid-row: span 3 / span 3;
  }

  .xl\:row-span-4 {
    grid-row: span 4 / span 4;
  }

  .xl\:row-start-1 {
    grid-row-start: 1;
  }

  .xl\:row-start-2 {
    grid-row-start: 2;
  }

  .xl\:row-end-2 {
    grid-row-end: 2;
  }

  .xl\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .xl\:mb-0 {
    margin-bottom: 0px;
  }

  .xl\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .xl\:ml-0 {
    margin-left: 0px;
  }

  .xl\:mr-0 {
    margin-right: 0px;
  }

  .xl\:mt-16 {
    margin-top: 4rem;
  }

  .xl\:mt-9 {
    margin-top: 2.25rem;
  }

  .xl\:block {
    display: block;
  }

  .xl\:inline {
    display: inline;
  }

  .xl\:flex {
    display: flex;
  }

  .xl\:grid {
    display: grid;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:aspect-\[5\/4\] {
    aspect-ratio: 5/4;
  }

  .xl\:aspect-video {
    aspect-ratio: 16 / 9;
  }

  .xl\:h-12 {
    height: 3rem;
  }

  .xl\:h-14 {
    height: 3.5rem;
  }

  .xl\:h-\[292px\] {
    height: 292px;
  }

  .xl\:h-\[360px\] {
    height: 360px;
  }

  .xl\:h-\[480px\] {
    height: 480px;
  }

  .xl\:h-\[640px\] {
    height: 640px;
  }

  .xl\:h-\[800px\] {
    height: 800px;
  }

  .xl\:h-full {
    height: 100%;
  }

  .xl\:w-1\/2 {
    width: 50%;
  }

  .xl\:w-1\/3 {
    width: 33.333333%;
  }

  .xl\:w-1\/4 {
    width: 25%;
  }

  .xl\:w-1\/6 {
    width: 16.666667%;
  }

  .xl\:w-2\/3 {
    width: 66.666667%;
  }

  .xl\:w-auto {
    width: auto;
  }

  .xl\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .xl\:w-full {
    width: 100%;
  }

  .xl\:max-w-7xl {
    max-width: 80rem;
  }

  .xl\:max-w-\[66\%\] {
    max-width: 66%;
  }

  .xl\:max-w-none {
    max-width: none;
  }

  .xl\:flex-1 {
    flex: 1 1 0%;
  }

  .xl\:grid-flow-col {
    grid-auto-flow: column;
  }

  .xl\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .xl\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .xl\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .xl\:grid-cols-none {
    grid-template-columns: none;
  }

  .xl\:grid-rows-1 {
    grid-template-rows: repeat(1, minmax(0, 1fr));
  }

  .xl\:grid-rows-2 {
    grid-template-rows: repeat(2, minmax(0, 1fr));
  }

  .xl\:flex-row {
    flex-direction: row;
  }

  .xl\:flex-col {
    flex-direction: column;
  }

  .xl\:flex-wrap {
    flex-wrap: wrap;
  }

  .xl\:items-start {
    align-items: flex-start;
  }

  .xl\:items-center {
    align-items: center;
  }

  .xl\:justify-normal {
    justify-content: normal;
  }

  .xl\:justify-start {
    justify-content: flex-start;
  }

  .xl\:justify-end {
    justify-content: flex-end;
  }

  .xl\:justify-between {
    justify-content: space-between;
  }

  .xl\:gap-0 {
    gap: 0px;
  }

  .xl\:gap-10 {
    gap: 2.5rem;
  }

  .xl\:gap-12 {
    gap: 3rem;
  }

  .xl\:gap-16 {
    gap: 4rem;
  }

  .xl\:gap-4 {
    gap: 1rem;
  }

  .xl\:gap-8 {
    gap: 2rem;
  }

  .xl\:gap-x-0 {
    -moz-column-gap: 0px;
         column-gap: 0px;
  }

  .xl\:gap-x-12 {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }

  .xl\:gap-x-16 {
    -moz-column-gap: 4rem;
         column-gap: 4rem;
  }

  .xl\:gap-x-32 {
    -moz-column-gap: 8rem;
         column-gap: 8rem;
  }

  .xl\:gap-x-4 {
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }

  .xl\:gap-x-8 {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }

  .xl\:gap-y-0 {
    row-gap: 0px;
  }

  .xl\:gap-y-12 {
    row-gap: 3rem;
  }

  .xl\:gap-y-16 {
    row-gap: 4rem;
  }

  .xl\:gap-y-32 {
    row-gap: 8rem;
  }

  .xl\:gap-y-4 {
    row-gap: 1rem;
  }

  .xl\:gap-y-8 {
    row-gap: 2rem;
  }

  .xl\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .xl\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .xl\:space-y-12 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(3rem * var(--tw-space-y-reverse));
  }

  .xl\:space-y-16 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(4rem * var(--tw-space-y-reverse));
  }

  .xl\:rounded {
    border-radius: 0.25rem;
  }

  .xl\:border-r {
    border-right-width: 1px;
  }

  .xl\:p-10 {
    padding: 2.5rem;
  }

  .xl\:p-8 {
    padding: 2rem;
  }

  .xl\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .xl\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .xl\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .xl\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .xl\:px-32 {
    padding-left: 8rem;
    padding-right: 8rem;
  }

  .xl\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .xl\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .xl\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .xl\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .xl\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .xl\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .xl\:py-48 {
    padding-top: 12rem;
    padding-bottom: 12rem;
  }

  .xl\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .xl\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .xl\:pl-12 {
    padding-left: 3rem;
  }

  .xl\:pl-20 {
    padding-left: 5rem;
  }

  .xl\:pr-12 {
    padding-right: 3rem;
  }

  .xl\:pr-20 {
    padding-right: 5rem;
  }

  .xl\:pr-8 {
    padding-right: 2rem;
  }

  .xl\:pt-0 {
    padding-top: 0px;
  }

  .xl\:pt-52 {
    padding-top: 13rem;
  }

  .xl\:text-left {
    text-align: left;
  }

  .xl\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .xl\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .xl\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .xl\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .xl\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1536px) {
  .\32xl\:mb-12 {
    margin-bottom: 3rem;
  }

  .\32xl\:mt-12 {
    margin-top: 3rem;
  }

  .\32xl\:aspect-\[4\/2\] {
    aspect-ratio: 4/2;
  }

  .\32xl\:-translate-y-4 {
    --tw-translate-y: -1rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .\32xl\:translate-x-44 {
    --tw-translate-x: 11rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .\32xl\:space-x-16 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(4rem * var(--tw-space-x-reverse));
    margin-left: calc(4rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .\32xl\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .\32xl\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }
}

.dark\:divide-gray-300:where(.dark, .dark *) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-divide-opacity));
}

.dark\:divide-white\/20:where(.dark, .dark *) > :not([hidden]) ~ :not([hidden]) {
  border-color: rgb(255 255 255 / 0.2);
}

.dark\:border-gray-200:where(.dark, .dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.dark\:border-gray-200\/10:where(.dark, .dark *) {
  border-color: rgb(229 231 235 / 0.1);
}

.dark\:border-gray-500:where(.dark, .dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}

.dark\:border-gray-600:where(.dark, .dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}

.dark\:border-gray-700:where(.dark, .dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}

.dark\:border-white\/20:where(.dark, .dark *) {
  border-color: rgb(255 255 255 / 0.2);
}

.dark\:border-white\/5:where(.dark, .dark *) {
  border-color: rgb(255 255 255 / 0.05);
}

.dark\:bg-black\/30:where(.dark, .dark *) {
  background-color: rgb(0 0 0 / 0.3);
}

.dark\:bg-gray-300:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.dark\:bg-gray-500:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.dark\:bg-gray-600:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.dark\:bg-gray-700:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark\:bg-gray-900\/40:where(.dark, .dark *) {
  background-color: rgb(17 24 39 / 0.4);
}

.dark\:bg-primary-500:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.dark\:bg-primary-600:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.dark\:bg-primary-800:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity));
}

.dark\:bg-white:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.dark\:bg-white\/10:where(.dark, .dark *) {
  background-color: rgb(255 255 255 / 0.1);
}

.dark\:bg-white\/5:where(.dark, .dark *) {
  background-color: rgb(255 255 255 / 0.05);
}

.dark\:text-gray-100:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.dark\:text-gray-200:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.dark\:text-gray-300:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.dark\:text-gray-400:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.dark\:text-gray-50:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity));
}

.dark\:text-gray-500:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.dark\:text-gray-600:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.dark\:text-primary-50:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(239 246 255 / var(--tw-text-opacity));
}

.dark\:text-primary-500:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.dark\:text-primary-highlight:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.dark\:text-red-600:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.dark\:text-white:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:placeholder\:text-gray-300:where(.dark, .dark *)::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.dark\:placeholder\:text-gray-300:where(.dark, .dark *)::placeholder {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.dark\:hover\:bg-gray-700:hover:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary-600:hover:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary-700:hover:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.dark\:hover\:text-gray-100:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.dark\:hover\:text-gray-200:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.dark\:hover\:text-primary-highlight:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.dark\:hover\:text-white:hover:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.group\/card:hover .dark\:group-hover\/card\:bg-gray-700:where(.dark, .dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.group\/card:hover .dark\:group-hover\/card\:text-primary-highlight:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.group:hover .dark\:group-hover\:text-primary-400:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.group:hover .dark\:group-hover\:text-primary-highlight:where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}

.dark\:prose-h3\:text-white :is(:where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:prose-p\:text-gray-100 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.dark\:prose-p\:text-gray-300 :is(:where(p):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.dark\:prose-strong\:text-secondary-500 :is(:where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *))):where(.dark, .dark *) {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .dark\:md\:border-gray-600:where(.dark, .dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99 / var(--tw-border-opacity));
  }

  .dark\:md\:border-gray-900\/20:where(.dark, .dark *) {
    border-color: rgb(17 24 39 / 0.2);
  }

  .dark\:md\:border-white\/5:where(.dark, .dark *) {
    border-color: rgb(255 255 255 / 0.05);
  }
}