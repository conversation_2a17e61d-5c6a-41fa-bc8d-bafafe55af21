url = "/blog/:slug"
layout = "default"
title = "Blogbericht"

[postDetail]
slug = "{{ :slug }}"

[postList]

[postList blogItems3]
maxItems = 3
sorting = "publication_date"
sortDirection = "desc"
hidePost = "{{ :slug }}"

[categoryList]
==
<?php

    function onEnd()
    {
        use Invato\SiteConfiguration\Models\SiteConfigSettings;
        $post = $this->components['postDetail'];
        $postTitle = $post->post?->title;
        $postMetaDesc = $post->post?->seo['meta_description'];
        $postOgDesc = $post->post?->seo['og_description'];
        $postShortDesc = $post->post?->excerpt;

        if (
            isset($post->post?->seo['meta_title'])
        &&
            trim($post->post?->seo['meta_title']) !== ''
            ) {
            $seoTitle = $post->post?->seo['meta_title'];
        }

        $seoDesc = $postShortDesc;
        if (
            isset($postOgDesc)
            &&
            trim($postOgDesc) !== ''
        ) {
            $seoDesc = $postOgDesc;
        }
        if (
            isset($postMetaDesc)
            &&
            trim($postMetaDesc) !== ''
        ) {
            $seoDesc = $postMetaDesc;
        }

        $seoTitleSuffix = SiteConfigSettings::get('meta_title_suffix');
        $title = $seoTitle ?? $postTitle;
        $this->page->title = $title . $seoTitleSuffix;
        $this->page->meta_description = $seoDesc;

        // Make post available for TOC generation
        $this->page['post'] = $post->post;
    }

?>
==

{% if not postDetail %}
    {% do abort(404) %}
{% endif %}

{% set allItems = postList.posts %}
{% set categories = categoryList.categories %}
{% set items = blogItems3.posts %}
{% set blogPage = postList.blogPage|link %}

{% set dark = '' %}

{% put scripts %}
    <script src="https://cdn.jsdelivr.net/npm/sharer.js@0.5.2/sharer.min.js"></script>
{% endput %}

{% put adminbar %}
    {% partial 'atomic/atoms/admin-bar-link' link="/backend/invato/blog/posts/update/" id=post.id label="Blogbericht bewerken" icon="fa-pen-to-square" %}
{% endput %}

<section
    data-name="blog-detail"
    data-category="blog"
    class="bg-gray-100 {{ dark }} dark:bg-gray-700">

    <div class="container">

        <div class="space-y-4 lg:space-y-0 lg:grid lg:grid-cols-12 lg:gap-4 xl:gap-x-8 xl:gap-y-4 py-8 lg:py-20">

            <div class="col-span-12 pl-2">
                {% partial 'atomic/atoms/blog/blog-breadcrumbs' blogPage=blogPage %}
            </div>

            {% if post.status == 'draft' %}
                {% set notificationText = 'Dit blog bericht is een concept. Publiceer het bericht zodat het zichtbaar is voor bezoekers van de website.'|_ %}
                <div class="col-span-12 pt-4">
                    {% partial 'ui/alert' text=notificationText type=danger %}
                </div>
            {% endif %}

            <div class="col-span-8 space-y-8 bg-white dark:bg-gray-500 md:rounded-lg py-8 px-4 -mx-4 md:mx-0 md:px-8">

                <div class="flex flex-wrap items-center gap-2">
                    {% for item in post.categories %}
                        {% partial 'atomic/atoms/batch-link' label=item.title link=categoryPage | page({ slug: item.slug }) %}
                    {% endfor %}
                </div>

                <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                    {% partial 'atomic/atoms/headings/header-h1' text=post.title %}
                </div>


                <div class="border-y border-gray-200">
                    {% if postList.showDates or postList.showAuthor %}
                    {% partial 'atomic/molecules/blog/blog-item-info' postList=postList %}
                    {% endif %}
                </div>

                <div class="space-y-8">

                    {% if post.excerpt %}
                        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                            <p class="lead">
                                {% partial 'atomic/molecules/content-section' content=post.excerpt class='' %}
                            </p>
                        </div>
                    {% endif %}

                    {% if post.image %}
                        <div class="flex items-center justify-center overflow-hidden">
                            {% partial 'atomic/atoms/media/image' class='w-auto h-full mx-auto rounded-md max-h-[320px] md:max-h-[440px] lg:max-h-[500px]' img=post.image title=post.img_title resize_w="750" %}
                        </div>
                    {% endif %}

                    {% if post.table_of_contents and post.table_of_contents|length > 0 %}
                        {% partial 'atomic/atoms/blog/table-of-contents' tableOfContents=post.table_of_contents %}
                    {% endif %}

                    {% if post.content %}
                        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                            {% partial 'atomic/molecules/content-section' content=post.content %}
                        </div>
                    {% endif %}

                    <div class="space-y-8">
                        {% for item in post.additional_content %}

                            {% if item.text %}
                                <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                                    {% partial 'atomic/molecules/content-section' content=item.text %}
                                </div>
                            {% endif %}

                            {% if item.img %}
                                <div class="flex items-center justify-center overflow-hidden">
                                    {% partial 'atomic/atoms/media/image' class='w-auto h-full mx-auto rounded-md max-h-[320px] md:max-h-[440px] lg:max-h-[500px]' img=item.img title=item.img_title resize_w="750" %}
                                </div>
                            {% endif %}

                            {% if item.cta %}

                                <div
                                    class="additional-content-cta relative bg-primary-200 dark:bg-primary-800 text-center rounded-lg space-y-4 py-8 px-4 md:px-0">

                                    {% partial 'atomic/molecules/content-heading-centered' box=item.cta %}

                                    {% if item.cta.buttons %}
                                        {% partial 'atomic/molecules/buttons' buttons=item.cta.buttons class="justify-center" %}
                                    {% endif %}

                                </div>

                            {% endif %}

                        {% endfor %}
                    </div>

                </div>

                <div class="border-t border-gray-500 dark:border-gray-200">
                    <div class="group pt-4">
                        <a href="{{ blogPage }}" class="flex items-center gap-2">
                            <i class="fa-regular fa-circle-chevron-left group-hover:text-primary dark:text-gray-50 dark:group-hover:text-primary-highlight"></i>
                            <span class="underline group-hover:text-primary dark:text-gray-50 dark:group-hover:text-primary-highlight">Terug naar het overzicht</span>
                        </a>
                    </div>
                </div>

            </div>

            {% partial 'atomic/organisms/blog/blog-sidebar' item=post %}

        </div>
    </div>
</section>

{# Add SEO meta tags to page #}
{% set seo_object = post %}
{% partial 'site/seo_meta' item = seo_object %}

{% set authorName = post.author.first_name ~ " " ~ post.author.last_name %}

{% put meta %}
    <script type="application/ld+json">
        {
        "@context": "https://schema.org/",
        "@type": "BlogPosting",
        "headline": "{{ post.title }}",
        "name": "{{ post.title }}",
        "description": "{{ post.excerpt }}",
        "datePublished": "{{ post.publication_date }}",
        "dateModified": "{{ post.updated_at }}",
        "timeRequired": "PT{{ post.reading_time }}S",
        "author": {
            "@type": "Person",
            "name": "{{ authorName }}",
            "image": {
                "@type": "ImageObject",
                "@id": "{{ post.author.avatar | media }}",
                "url": "{{ post.author.avatar | media }}"
            }
        },
        "publisher": {
            "@type": "Organization",
            "@id": "https://dataliberate.com",
            "name": "{{ company.name }}",
            "logo": {
                "@type": "ImageObject",
                "@id": "{{ logos.primary | media }}",
                "url": "{{ logos.primary | media }}"
            }
        },
        "image": {
            "@type": "ImageObject",
            "@id": "{{ post.image | media }}",
            "url": "{{ post.image | media }}"
        },
        "url": "{{ postPage | page({ slug: post.slug }) }}",
        "wordCount": "{{ post.word_count }}"
    }
    </script>
{% endput %}

