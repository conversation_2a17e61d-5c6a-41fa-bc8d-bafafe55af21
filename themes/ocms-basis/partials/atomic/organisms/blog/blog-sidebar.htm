<div class="col-span-4 pt-12 lg:pt-0 flex flex-col gap-8 md:w-2/3 md:mx-auto lg:gap-4 lg:w-full xl:gap-8">

    {% for item in allItems %}
        {% if item.is_featured and item.slug != slug %}
            <div class="bg-white dark:bg-gray-500 md:rounded-lg p-4 -mx-4 md:mx-0 md:p-8 lg:p-4 xl:p-8 order-1 lg:order-3">
                <div class="space-y-4">
                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                        {% set sidebarLabel = 'Uitgelicht:'|_ %}
                        {% partial 'atomic/atoms/cards/card-heading' text=sidebarLabel level="4" class="font-bold" %}
                    </div>

                    {% partial 'atomic/molecules/blog/blog-item-card-small' item=item class='' %}
                </div>
            </div>
        {% endif %}
    {% endfor %}


    {% if item.cta_title or item.cta_content or item.cta_button %}
        <div class="bg-white dark:bg-gray-500 md:rounded-lg p-4 -mx-4 md:mx-0 md:p-8 lg:p-4 xl:p-8 order-1 lg:order-3">
            {% partial 'atomic/molecules/blog/blog-cta-card' item=item %}
        </div>
    {% endif %}

    <div class="bg-white dark:bg-gray-500 md:rounded-lg p-4 -mx-4 md:mx-0 md:p-8 lg:p-4 xl:p-8 order-1 lg:order-3">
        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
            {% set sidebarLabel = 'Overige artikelen:'|_ %}
            {% partial 'atomic/atoms/cards/card-heading' text=sidebarLabel level="4" class="font-bold" %}
        </div>
        <div class="divide-y divide-gray-400 dark:divide-gray-300">
            {% for item in items %}
                {% if not item.is_featured and item.slug != slug %}

                    {% partial 'atomic/molecules/blog/blog-item-card-xs' item=item class=' py-4' %}

                {% endif %}
            {% endfor %}
        </div>
    </div>

    <div class="flex flex-col gap-8 lg:gap-4 xl:gap-8 order-last">
        {% if categories.toArray() %}
            <div class="bg-white dark:bg-gray-500 md:rounded-lg p-4 -mx-4 md:mx-0 md:p-8 lg:p-4 xl:p-8">
                <div class="space-y-4">
                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                        {% set sidebarLabel = 'Categorieën:'|_ %}
                        {% partial 'atomic/atoms/cards/card-heading' text=sidebarLabel level="4" class="font-bold" %}
                    </div>
                    <div class="space-y-4">
                        {% for item in categories %}
                            {% partial 'atomic/molecules/blog/blog-category-card' item=item %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}

        {% if this.request.host == 'invato-basis.test' or this.request.host == 'dev-staging.ocms.io' %}
            <div class="bg-white dark:bg-gray-500 md:rounded-lg p-4 -mx-4 md:mx-0 md:p-8 lg:p-4 xl:p-8">
                <div class="space-y-4">
                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                        {% set sidebarLabel = 'Deel dit bericht:'|_ %}
                        {% partial 'atomic/atoms/cards/card-heading' text=sidebarLabel level="4" class="font-bold" %}
                    </div>
                    <div class="grid grid-cols-5 gap-4">
                        <button title="{{ 'Deel op X'|_ }}" class="button" data-sharer="x" data-title="{{ post.title }}" data-url="{{ postPage | page({ slug: post.slug }) }}">
                            <i class="fa-brands fa-x-twitter fa-xl"></i>
                        </button>
                        <button title="{{ 'Deel op Threads'|_ }}" class="button" data-sharer="threads" data-title="{{ post.title }}" data-url="{{ postPage | page({ slug: post.slug }) }}">
                            <i class="fa-brands fa-threads fa-xl text-social-instagram"></i>
                        </button>
                        <button title="{{ 'Deel op Facebook'|_ }}" class="button" data-sharer="facebook" data-title="{{ post.title }}" data-url="{{ postPage | page({ slug: post.slug }) }}">
                            <i class="fa-brands fa-facebook fa-xl text-social-facebook"></i>
                        </button>
                        <button title="{{ 'Deel op LinkedIn'|_ }}" class="button" data-sharer="linkedin" data-title="{{ post.title }}" data-url="{{ postPage | page({ slug: post.slug }) }}">
                            <i class="fa-brands fa-linkedin fa-xl text-social-linkedin"></i>
                        </button>
                        <button title="{{ 'Deel via mail'|_ }}" class="button" data-sharer="email" data-title="{{ post.title }}" data-url="{{ postPage | page({ slug: post.slug }) }}">
                            <i class="fa-solid fa-envelope fa-xl text-gray-400"></i>
                        </button>
                        <button title="{{ 'Deel via WhatsApp'|_ }}" class="button" data-sharer="whatsapp" data-title="{{ post.title }}" data-url="{{ postPage | page({ slug: post.slug }) }}">
                            <i class="fa-brands fa-whatsapp fa-xl text-social-whatsapp"></i>
                        </button>
                        <button title="{{ 'Deel via Telegram'|_ }}" class="button" data-sharer="telegram" data-title="{{ post.title }}" data-url="{{ postPage | page({ slug: post.slug }) }}">
                            <i class="fa-brands fa-telegram fa-xl text-social-twitter"></i>
                        </button>
                    </div>
                </div>
            </div>
        {% endif %}

        {% if socials %}
            <div class="bg-white dark:bg-gray-500 md:rounded-lg p-4 -mx-4 md:mx-0 md:p-8 lg:p-4 xl:p-8">
                <div class="space-y-8">

                    {#                            <div class="space-y-4"> #}
                    {#                                <div class="prose prose-primary dark:prose-primary_inverted max-w-none"> #}
                    {#                                    {% set sidebarLabel = 'Deel dit bericht op:'|_ %} #}
                    {#                                    {% partial 'atomic/atoms/headings/header-h3' text=sidebarLabel %} #}
                    {#                                </div> #}
                    {#                                <div class="grid grid-cols-2 gap-4"> #}
                    {#                                    {% for item in socials %} #}
                    {#                                        {% partial 'atomic/atoms/blog/blog-socials' item=item %} #}
                    {#                                    {% endfor %} #}
                    {#                                </div> #}
                    {#                            </div> #}

                    <div class="space-y-4">
                        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                            {% set sidebarLabel = 'Volg ons op:'|_ %}
                            {% partial 'atomic/atoms/cards/card-heading' text=sidebarLabel level="4" class="font-bold" %}
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            {% for item in socials %}
                                {% partial 'atomic/atoms/blog/blog-socials' item=item %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

</div>
