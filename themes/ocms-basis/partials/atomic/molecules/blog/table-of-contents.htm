{% if tableOfContents and tableOfContents|length > 0 %}
<div class="table-of-contents bg-gray-50 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded-lg p-6 mb-8 {{ class }}">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
        <i class="fa-regular fa-list mr-2"></i>
        {{ 'Inhoudsopgave'|_ }}
    </h3>

    <nav class="toc-nav">
        <ul class="space-y-2">
            {% for item in tableOfContents %}
                {% if item.is_title or not item.id %}
                    {# Unclickable title entry - use title atom #}
                    {% partial 'atomic/atoms/blog/toc-item-title'
                        text=item.text
                        level=item.level %}
                {% else %}
                    {# Clickable content heading - use link atom #}
                    {% partial 'atomic/atoms/blog/toc-item-link'
                        text=item.text
                        level=item.level
                        id=item.id %}
                {% endif %}
            {% endfor %}
        </ul>
    </nav>
</div>

<style>
.toc-nav a:hover {
    text-decoration: underline;
}

.toc-level-1 a {
    font-weight: 600;
}

.toc-level-2 a {
    font-weight: 500;
}

.toc-level-3 a,
.toc-level-4 a {
    font-weight: 400;
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Add some offset for fixed headers if needed */
.prose h1[id],
.prose h2[id],
.prose h3[id],
.prose h4[id] {
    scroll-margin-top: 2rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to generate heading ID from text (matches PHP logic exactly)
    function generateHeadingId(text) {
        let id = text.toLowerCase();
        // Remove non-alphanumeric characters except spaces and hyphens
        id = id.replace(/[^a-z0-9\s-]/g, '');
        // Replace multiple spaces/hyphens with single hyphen
        id = id.replace(/[\s-]+/g, '-');
        // Remove leading/trailing hyphens
        id = id.replace(/^-+|-+$/g, '');

        return id || 'heading-' + Math.random().toString(36).substr(2, 9);
    }

    // Get TOC data to match headings with their intended IDs
    const tocLinks = document.querySelectorAll('.toc-link');
    const tocData = {};

    tocLinks.forEach(function(link) {
        const headingText = link.getAttribute('data-heading-text');
        const targetId = link.getAttribute('href').substring(1);
        tocData[headingText] = targetId;
    });

    // Add IDs to all headings in the content areas
    const contentAreas = document.querySelectorAll('.prose, .blog-post-content, .space-y-8, .col-span-8');

    // Also try to find headings in the entire document if content areas are not found
    if (contentAreas.length === 0) {
        const allHeadings = document.querySelectorAll('h1, h2, h3, h4');
        allHeadings.forEach(function(heading) {
            processHeading(heading);
        });
    } else {
        contentAreas.forEach(function(contentArea) {
            const headings = contentArea.querySelectorAll('h1, h2, h3, h4');
            headings.forEach(function(heading) {
                processHeading(heading);
            });
        });
    }

    function processHeading(heading) {
        if (!heading.id) {
            const text = heading.textContent.trim();

            // Use the exact ID from TOC data if available, otherwise generate one
            const id = tocData[text] || generateHeadingId(text);
            heading.id = id;

            // Add scroll margin for better positioning
            heading.style.scrollMarginTop = '2rem';

            console.log('Added ID to heading:', text, '-> ID:', id);
        }
    }

    // Handle TOC link clicks for smooth scrolling
    tocLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                // Smooth scroll to element
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Update URL hash
                if (history.pushState) {
                    history.pushState(null, null, '#' + targetId);
                } else {
                    window.location.hash = targetId;
                }

                // Optional: Add visual feedback
                targetElement.style.transition = 'background-color 0.3s ease';
                targetElement.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
                setTimeout(function() {
                    targetElement.style.backgroundColor = '';
                }, 1000);
            } else {
                console.warn('TOC: Target element not found for ID:', targetId);
            }
        });
    });

    // Handle direct hash navigation (when someone visits a URL with #heading)
    if (window.location.hash) {
        setTimeout(function() {
            const targetElement = document.querySelector(window.location.hash);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }, 100);
    }

    // Run again after a delay to catch any dynamically loaded content
    setTimeout(function() {
        const allHeadings = document.querySelectorAll('h1, h2, h3, h4');
        allHeadings.forEach(function(heading) {
            if (!heading.id) {
                const text = heading.textContent.trim();
                const id = tocData[text] || generateHeadingId(text);
                heading.id = id;
                heading.style.scrollMarginTop = '2rem';
                console.log('Delayed: Added ID to heading:', text, '-> ID:', id);
            }
        });
    }, 500);
});
</script>
{% endif %}
