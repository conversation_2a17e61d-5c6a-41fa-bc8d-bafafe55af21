{# TOC Title Item Atom (Unclickable) #}
<li class="toc-item toc-level-{{ level }}">
    <span class="flex items-start text-sm text-gray-900 dark:text-gray-100 font-bold
                 {% if level == 1 %}{% endif %}
                 {% if level == 2 %}pl-4{% endif %}
                 {% if level == 3 %}pl-8{% endif %}
                 {% if level == 4 %}pl-12{% endif %}">
        <span class="inline-block w-2 h-2 rounded-full bg-current opacity-80 mt-2 mr-3 flex-shrink-0"></span>
        <span class="flex-1">{{ text }}</span>
    </span>
</li>
