{% if tableOfContents and tableOfContents|length > 0 %}
<div class="table-of-contents bg-gray-50 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded-lg p-6 mb-8 {{ class }}">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
        <i class="fa-regular fa-list mr-2"></i>
        {{ 'Inhoudsopgave'|_ }}
    </h3>

    <nav class="toc-nav">
        <ul class="space-y-2">
            {% for item in tableOfContents %}
                <li class="toc-item toc-level-{{ item.level }}">
                    <a href="#{{ item.id }}"
                       class="toc-link block text-sm text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-highlight transition-colors duration-200
                              {% if item.level == 1 %}font-semibold{% endif %}
                              {% if item.level == 2 %}pl-4{% endif %}
                              {% if item.level == 3 %}pl-8{% endif %}
                              {% if item.level == 4 %}pl-12{% endif %}"
                       data-heading-text="{{ item.text }}">
                        {{ item.text }}
                    </a>
                </li>
            {% endfor %}
        </ul>
    </nav>
</div>

<style>
.toc-nav a:hover {
    text-decoration: underline;
}

.toc-level-1 a {
    font-weight: 600;
}

.toc-level-2 a {
    font-weight: 500;
}

.toc-level-3 a,
.toc-level-4 a {
    font-weight: 400;
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Add some offset for fixed headers if needed */
.prose h1[id],
.prose h2[id],
.prose h3[id],
.prose h4[id] {
    scroll-margin-top: 2rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to generate heading ID from text
    function generateHeadingId(text) {
        return text.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/[\s-]+/g, '-')
            .replace(/^-+|-+$/g, '') || 'heading-' + Math.random().toString(36).substr(2, 9);
    }

    // Add IDs to all headings in the content
    const contentArea = document.querySelector('.blog-post-content, .prose');
    if (contentArea) {
        const headings = contentArea.querySelectorAll('h1, h2, h3, h4');

        headings.forEach(function(heading) {
            if (!heading.id) {
                const text = heading.textContent.trim();
                const id = generateHeadingId(text);
                heading.id = id;
            }
        });
    }

    // Handle TOC link clicks for smooth scrolling
    const tocLinks = document.querySelectorAll('.toc-link');
    tocLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Update URL hash
                history.pushState(null, null, '#' + targetId);
            }
        });
    });
});
</script>
{% endif %}
