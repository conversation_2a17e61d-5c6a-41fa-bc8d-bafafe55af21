<!DOCTYPE html>
<html lang="<?= App::getLocale() ?>" class="no-js">
    <head>
        <link rel="icon" type="image/png" href="<?= e(Backend\Models\BrandSetting::getFavicon() ?: Backend::skinAsset('assets/images/favicon.png')) ?>">
        <title><?= __('Administration Area') ?> | <?= e(Backend\Models\BrandSetting::get('app_name')) ?></title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=0">
        <meta name="robots" content="noindex">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="backend-base-path" content="<?= Backend::baseUrl() ?>">
        <meta name="csrf-token" content="<?= csrf_token() ?>">
        <meta name="turbo-visit-control" content="disable">
        <link rel="preload" href="/app/assets/images/login-bg.webp" as="image" type="image/webp">
        <?php

                                            use Media\Classes\MediaLibrary;

            $coreBuild = Backend::assetVersion();

            $styles = [
                Backend::skinAsset('assets/vendor/bootstrap/bootstrap.css'),
                Backend::skinAsset('assets/css/october.css'),
            ];

            $scripts = [
                Url::asset('modules/system/assets/js/vendor/jquery.min.js'),
                Url::asset('modules/system/assets/js/framework-bundle.min.js'),
                Backend::skinAsset('assets/vendor/bootstrap/bootstrap.min.js'),
                Backend::skinAsset('assets/js/vendor-min.js'),
                Backend::skinAsset('assets/js/october-min.js'),
                Url::asset('modules/system/assets/js/vue.bundle-min.js'),
                Url::to('modules/backend/assets/js/auth/auth.js'),
                Url::asset('modules/system/assets/js/lang/lang.'.App::getLocale().'.js'),
            ];
        ?>
        <?php foreach ($styles as $style): ?>
            <link href="<?= $style . '?v=' . $coreBuild ?>" rel="stylesheet" importance="high" />
        <?php endforeach ?>

        <?php foreach ($scripts as $script): ?>
            <script src="<?= $script . '?v=' . $coreBuild ?>" importance="high"></script>
        <?php endforeach ?>

        <?php if (!Config::get('backend.enable_service_workers', false)): ?>
            <script> unregisterServiceWorkers() </script>
        <?php endif ?>

        <?= $this->makeAssets() ?>
        <?= Block::placeholder('head') ?>
        <?= $this->makeLayoutPartial('custom_styles') ?>
        <?= $this->fireViewEvent('backend.layout.extendHead', ['auth']) ?>
        <?php
            $customizationVars = Backend\Classes\LoginCustomization::getCustomizationVariables($this);
            $logo = '/app/assets/images/logo.png';
            $bgImage = '/app/assets/images/login-bg.webp';

            $loginCustomization = $customizationVars->loginCustomization;
            $loginBackgroundType = $loginCustomization->loginBackgroundType;
            $defaultImage1x = $customizationVars->defaultImage1x;
            $defaultImage2x = $customizationVars->defaultImage2x;

            if ($loginBackgroundType === 'october_ai_images') {
                $aiImageIndex = rand(0, 8);
                $generatedImageData = Backend\Classes\LoginCustomization::getGeneratedImageData();
            }
        ?>
        <style>
            .signin .outer-form-container h1 { margin-bottom: 54px; }
            .signin .invato-login-card .login-body p,
            .signin .invato-login-card .login-body h2 { display: none; }

            @media (max-width: 576px) {
                body.outer .outer-theme-cell {
                    display: block;
                }
            }


            @keyframes fadeInUp {
                0% {
                    opacity: .2;
                    transform: translateY(30%) scale(70%);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0) scale(100%);
                }
            }

            .fadeInUp {
                animation: fadeInUp .5s;
                -webkit-animation-fill-mode: forwards;
                -moz-animation-fill-mode: forwards;
                -ms-animation-fill-mode: forwards;
                -o-animation-fill-mode: forwards;
                animation-fill-mode: forwards;
            }

            @media (prefers-reduced-motion) {
                .fadeInUp {
                animation: none;
                }
            }


        </style>
    </head>
    <body class="outer <?= $this->bodyClass ?> message-outer-layout">
        <div class="position-absolute w-100 h-100" style="background-image: url('<?= e($bgImage) ?>'); background-size: cover;"></div>
        <div id="layout-canvas">
            <div class="d-flex w-100 h-100 align-items-center">
                <div class="container">
                    <div class="row">
                        <div class="col-sm-9 col-md-6 col-xl-4">
                            <article class="card invato-login-card bg-white shadow rounded-5 fadeInUp">
                                <section class="card-body p-5">
                                    <h1 class="mb-5">
                                        <?= e(Backend\Models\BrandSetting::get('app_name')) ?>
                                        <img src="<?= e($logo) ?>" style="max-width: 180px" alt="" />
                                    </h1>
                                    <!-- <p class="lead fw-medium mb-4"><?= e(Backend\Models\BrandSetting::get('login_prompt')) ?></p> -->
                                     <h5 class="card-title mb-5"><?= e(Backend\Models\BrandSetting::get('login_prompt')) ?></h5>
                                    <div class="login-body"><?= Block::placeholder('body') ?></div>
                                </section>
                                <footer class="card-footer py-3 px-5">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <a href="https://invato.nl/support" class="text-dark fw-medium" target="_blank">Vragen of hulp nodig?</a>
                                        </div>
                                        <div>
                                            by <a href="https://invato.nl/" class="fw-medium text-dark" target="_blank" title="Ga naar de website van Invato">Invato</a>
                                        </div>
                                    </div>
                                </footer>
                            </article>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- Flash Messages -->
        <div id="layout-flash-messages"><?= $this->makeLayoutPartial('flash_messages') ?></div>

        <?= $this->makeLayoutPartial('vue_templates') ?>
        <?= Block::placeholder('footer') ?>

        <?php if ( env('APP_ENV') === 'local' ): ?>
            <script>
                let user = document.getElementById('login-input');
                let pass = document.getElementById('password-input');

                user.value = "<EMAIL>";
                pass.value = "password";
            </script>
        <?php endif ?>
    </body>
</html>
