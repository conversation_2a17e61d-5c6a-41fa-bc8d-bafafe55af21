<?php

use Backend\Models\BrandSetting;

$activeItem = BackendMenu::getActiveMainMenuItem();
$navbarMode = BrandSetting::get('menu_mode', BrandSetting::MENU_INLINE);
$context = BackendMenu::getContext();
$isVerticalMenu = isset($isVerticalMenu) ? $isVerticalMenu : false;
$navLogo = BrandSetting::getNavLogo();
?>

<div class="invato-topbar">
    <div class="row justify-content-end">
        <div class="col-auto">
            &nbsp;
        </div>

        <?php
            if ( is_countable($this->user->access_to_websites) ) {
                if (count($this->user->access_to_websites) >= 2) {
        ?>
                    <div class="col-auto ms-auto">
                        <!-- TODO: create actual nice working layout -->
                        <?= $this->makeLayoutPartial('invato/website_selector') ?>
                    </div>
        <?php
                }
            }
        ?>

        <div class="col-auto invato-topbar-siteswitcher">
            <?php if ($siteSwitcher = $this->getWidget('siteSwitcher')) { ?>
                <?= $siteSwitcher->render(['isVerticalMenu' => $isVerticalMenu]) ?>
            <?php } ?>
            <?php if ($siteSwitcher = $this->getWidget('siteSwitcher')): ?>
                <?= $siteSwitcher->renderSubmenu(['isVerticalMenu' => $isVerticalMenu]) ?>
            <?php endif ?>
        </div>
        <div class="col-auto invato-topbar-account">
            <ul class="mainmenu-items mainmenu-extras" data-main-menu>
                <li class="mainmenu-item mainmenu-account has-subitems <?= ! $isVerticalMenu ? 'hidden-xs' : '' ?>" data-submenu-index="account">
                    <a href="javascript:">
                        <span class="nav-icon">
                            <img
                                src="<?= $this->user->getAvatarThumb(168, ['mode' => 'crop', 'extension' => 'png']) ?>"
                                loading="lazy"
                                width="32"
                                height="32" />
                        </span>
                        <?php if ($isVerticalMenu) { ?>
                            <span class="nav-label">
                                <?= e($this->user->full_name ?: $this->user->login) ?>
                            </span>
                        <?php } ?>
                    </a>
                </li>
            </ul>
            <ul class="mainmenu-items mainmenu-submenu-dropdown hover-effects" data-submenu-index="account">
                <li class="mainmenu-item section-title section-user-title">
                    <span class="nav-label"><?= e($this->user->full_name ?: $this->user->login) ?></span>
                </li>
                <?= $this->makeLayoutPartial('my_settings_menu_items', ['context' => $context]) ?>
            </ul>
        </div>
    </div>
</div>
