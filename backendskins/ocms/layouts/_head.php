<title data-title-template="<?= empty($this->pageTitleTemplate) ? '%s' : e($this->pageTitleTemplate) ?> | <?= e(Backend\Models\BrandSetting::get('app_name')) ?>">
    <?= e(__($this->pageTitle)) ?> | <?= e(Backend\Models\BrandSetting::get('app_name')) ?>
</title>
<?php if ($customFavicon = Backend\Models\BrandSetting::getFavicon()) { ?>
    <link rel="icon" type="image/png" href="<?= e($customFavicon) ?>">
<?php } else { ?>
    <link rel="icon" type="image/png" href="<?= e(Backend::skinAsset('assets/images/favicon.png')) ?>" data-favicon-dark="<?= e(Backend::skinAsset('assets/images/favicon-dark.png')) ?>">
<?php } ?>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=0">
<meta name="robots" content="noindex">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="app-timezone" content="<?= e(Config::get('app.timezone')) ?>">
<meta name="backend-base-path" content="<?= Backend::baseUrl() ?>">
<meta name="backend-timezone" content="<?= e(Backend\Models\Preference::get('timezone')) ?>">
<meta name="backend-locale" content="<?= e(Backend\Models\Preference::get('locale')) ?>">
<meta name="backend-site" content="<?= Site::getEditSiteId() ?>">
<meta name="csrf-token" content="<?= csrf_token() ?>">
<meta name="turbo-root" content="<?= Backend::baseUrl() ?>">
<?php if ($this->turboVisitControl) { ?>
    <meta name="turbo-visit-control" content="<?= $this->turboVisitControl ?>" />
<?php } ?>
<?php
$coreBuild = Backend::assetVersion();

$styles = [
    Backend::skinAsset('assets/vendor/bootstrap/bootstrap.css'),
    Backend::skinAsset('assets/vendor/bootstrap-icons/bootstrap-icons.css'),
    Backend::skinAsset('assets/css/october.css'),
    //        Backend::skinAsset('assets/css/invato-backend.css'),
];

$scripts = [
    Url::asset('modules/system/assets/js/vendor/jquery.min.js'),
    Url::asset('modules/system/assets/js/vue.bundle-min.js'),
    Url::asset('modules/system/assets/js/framework-bundle.min.js'),
    Backend::skinAsset('assets/vendor/bootstrap/bootstrap.min.js'),
    Backend::skinAsset('assets/js/vendor-min.js'),
    Backend::skinAsset('assets/js/october-min.js'),
    Url::asset('modules/system/assets/js/lang/lang.'.App::getLocale().'.js'),
];
?>
<style>
    .ocms-logo {
        width: 100%;
        padding: 8px 0;
        margin-bottom: 12px;
        background-color: rgba(0,0,0,.2);
    }

    .ocms-logo img {
        max-width: 100%;
        height: 40px;
        display: block;
        margin: 0 20px;
    }

    .invato-mainmenu-wrapper {
        width: 255px !important;
        position: relative;
        height: 100%;
        overflow: hidden;
    }

    .invato-mainmenu {
        background-color: var(--oc-mainnav-bg);
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        width: 255px;
        overflow-y: auto;
        padding-bottom: 32px;
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    .invato-mainmenu::-webkit-scrollbar {
        display: none; /* Chromium */
    }

    .invato-mainmenu .control-toolbar {
        padding: 0;
        display: block;
    }

    .invato-mainmenu > .main-menu-container > .navbar > ul.mainmenu-items { width: 100%; }

    .invato-mainmenu .mainmenu-items .mainmenu-item {
        position: relative;
        padding: 8px 16px;
        margin-bottom: 6px;
    }

    .invato-mainmenu .mainmenu-items .mainmenu-item a {
        display: flex;
        align-items: center;
    }

    .invato-mainmenu .mainmenu-items .mainmenu-item a .submenu-indicator {
        font-size: 16px;
    }

    .invato-mainmenu .mainmenu-items .mainmenu-item a[aria-expanded="true"] .submenu-indicator {
        transform: rotate(180deg);
    }

    .invato-mainmenu .mainmenu-items .mainmenu-item:hover,
    .invato-mainmenu .mainmenu-items .mainmenu-item.active {
        background-color: rgba(255, 255, 255, .05);
    }

    .invato-mainmenu .mainmenu-general .nav-label {
        font-size: 18px;
        font-weight: 400;
    }

    .invato-mainmenu .mainmenu-item .nav-icon {
        position: static;
        margin-right: 10px;
        font-size: 20px;
        width: 32px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .invato-mainmenu .mainmenu-items .mainmenu-item .nav-icon img {
        width: 24px;
        height: 24px;
    }

    .invato-mainmenu .invato-submenu {
        border-left: 1px solid rgba(255, 255, 255, .2);
        margin-left: 17px;
    }

    .invato-submenu-items {
        display: block;
        width: 100%;
        padding: 8px 0;
    }

    .invato-submenu-items .mainmenu-item.section-title {
        color: var(--oc-mainnav-color);
        padding: 8px 8px 6px 16px;
        margin-bottom: 4px;
        display: block;
        /* border-bottom: 1px solid rgba(255, 255, 255, 0.1); */
    }

    .invato-submenu-items .mainmenu-item.section-title .nav-label {
        font-size: 14px !important;
        font-weight: bold;
        text-transform: uppercase;
        color: var(--oc-mainnav-color);
        opacity: .6;
        padding-left: 0px;
    }

    .mainmenu-item:hover .mainmenu-item.section-title .nav-label {
        opacity: .6 !important;
    }

    .invato-mainmenu .mainmenu-items .mainmenu-item.section-title:hover {
        background-color: transparent;
    }

    .invato-mainmenu .submenu-item {
        margin-bottom: 12px;
        position: relative;
        padding: 0 12px 0 24px;
    }

    .invato-mainmenu .submenu-item:last-child {
        margin-bottom: 0;
    }

    .invato-mainmenu .submenu-item a {
        position: relative;
    }

    .invato-mainmenu .submenu-item:before {
        content: '';
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 1px;
        background-color: rgba(255, 255, 255, .3);
        left: 0;
    }

    .invato-mainmenu .submenu-item .nav-label {
        color: var(--oc-mainnav-color);
        opacity: .5;
        font-size: 16px;
    }

    body:not(.drag) .mainmenu-item:hover .submenu-item .nav-label,
    body:not(.drag) .invato-mainmenu .submenu-item:not(.active) .nav-label {
        color: var(--oc-mainnav-color);
        opacity: .5;
    }

    body:not(.drag) .invato-mainmenu .submenu-item:hover a:hover {
        text-decoration: none;
    }

    body:not(.drag) .invato-mainmenu .submenu-item:hover .nav-label,
    body:not(.drag) .invato-mainmenu .submenu-item.active .nav-label {
        opacity: 1;
    }

    .invato-mainmenu .submenu-item .nav-icon {
        position: static;
        margin-right: 8px;
        font-size: 20px;
        width: 24px;
    }

    html[data-bs-theme="light"] .invato-topbar {
        background-color: var(--bs-modal-bg);
    }

    html[data-bs-theme="dark"] .invato-topbar {
        background-color: var(--bs-modal-bg);
    }

    .invato-topbar {
        padding: 12px 20px;
        box-shadow: 0 0 2px rgba(var(--bs-body-color-rgb), .1);
        position: relative;
        z-index: 490;
    }

    .invato-topbar .topbar-col {
        margin: 0 16px;
    }

    .invato-topbar .website-selector {
        margin-left: auto;
    }

    .invato-topbar .mainmenu-item a {
        color: var(--bs-link-color-rgb);
    }

    .invato-topbar .invato-topbar-account .mainmenu-submenu-dropdown .mainmenu-item a {
        color: var(--oc-mainnav-color);
    }

    .invato-topbar .mainmenu-preview a {
        align-items: center;
    }

    .invato-topbar .mainmenu-preview .nav-icon {
        position: static;
        font-size: 18px;
        margin-right: 4px;
    }

    .invato-topbar .mainmenu-account .nav-icon {
        position: static;
        margin-right: 4px;
    }

    .invato-topbar .mainmenu-account .nav-icon img {
        border-radius: 4px;
    }

    .invato-topbar-account {
        position: relative;
    }

    .invato-topbar .invato-topbar-account:hover .mainmenu-submenu-dropdown {
        display: block;
        right: 0;
    }

    .invato-topbar .mainmenu-account a {
        align-items: center !important;
    }

    .invato-topbar div.mainmenu-account-avatar img {
        width: 24px;
        height: 24px;
    }

    .inv-website-dropdown-trigger {
        padding-right: 8px;
    }

    .inv-dropdown-btn-icon,
    .inv-dropdown-btn-icon-active {
        margin-left: 8px;
    }

    .inv-website-dropdown-trigger .inv-dropdown-btn-icon-active {
        display: none;
    }

    .inv-website-dropdown-trigger.active .inv-dropdown-btn-icon {
        display: none;
    }

    .inv-website-dropdown-trigger.active .inv-dropdown-btn-icon-active {
        display: inline;
    }

    .inv-website-selector {
        position: relative;
    }

    .inv-website-selector-dropdown {
        position: absolute;
        left: 0;
    }

    .inv-website-selector-dropdown {
        background: var(--oc-mainnav-bg);
        border-radius: 4px;
        width: 260px;
        height: 400px;
        padding: 10px 0;
        margin-top: 2px;
        display: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, .3);
    }

    .inv-website-selector-dropdown.show {
        display: block;
    }

    .inv-website-list {
        height: 330px;
        overflow-y: scroll;
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    .inv-website-list::-webkit-scrollbar {
        display: none;
    }

    .inv-search {
        padding: 7px 17px 12px 17px;
    }

    .inv-search input {
        width: 100%;
        appearance: none;
        padding: 7px 17px;
        border: 1px solid transparent;
        background-color: rgba(255, 255, 255, .2);
        border-radius: 4px;
        color: #fff;
    }

    .inv-search input::placeholder {
        color: #d2d2d2;
    }

    .inv-search input:focus {
        border: 1px solid rgba(255, 255, 255, .3);
        outline: 0;
        background-color: rgba(255, 255, 255, .25);
    }

    .inv-website-selector-dropdown .inv-website-selector-site {
        display: block;
        padding: 7px 17px 7px 17px;
        color: var(--oc-mainnav-color);
        transition: all .15s linear;
        font-size: 16px;
    }

    .inv-website-selector-dropdown .inv-website-selector-site:hover {
        text-decoration: none;
        background: rgba(255, 255, 255, .1);
    }

    .invato-topbar-siteswitcher {
        display: flex;
        align-items: center;
        position: relative;
    }

    .invato-topbar .mainmenu-preview.has-subitems .nav-icon {
        color: var(--bs-link-color-rgb);
    }

    .invato-topbar .invato-topbar-siteswitcher:hover .siteswitcher-dropdown {
        display: block;
        top: 100%;
        left: 0;
    }

    .invato-topbar .invato-topbar-siteswitcher .mainmenu-item a {
        color: var(--bs-link-color-rgb);
    }

    .invato-topbar .invato-topbar-siteswitcher .mainmenu-submenu-dropdown .mainmenu-item a {
        color: var(--oc-mainnav-color);
    }

    .flyout-overlay {
        left: 655px !important;
    }

</style>

<?php foreach ($styles as $style) { ?>
    <link href="<?= $style.'?v'.$coreBuild ?>" rel="stylesheet" importance="high" />
<?php } ?>

<?php foreach ($scripts as $script) { ?>
    <script src="<?= $script.'?v'.$coreBuild ?>" importance="high"></script>
<?php } ?>

<?php if (! Config::get('backend.enable_service_workers', false)) { ?>
    <script> unregisterServiceWorkers() </script>
<?php } ?>

<?= $this->makeAssets() ?>
<?= Block::placeholder('head') ?>
<?= $this->makeLayoutPartial('custom_styles') ?>
