<?php foreach (BackendMenu::listMainMenuItemsWithSubitems() as $itemIndex => $itemInfo) { ?>
    <?php
        $item = $itemInfo->mainMenuItem;
    $isActive = BackendMenu::isMainMenuItemActive($item);
    $isDashboard = $item->owner === 'October.Backend' && $item->code === 'dashboard';
    if ($isDashboard && ($customDashIcon = Backend\Models\BrandSetting::getNavDashboardIcon())) {
        $item->iconSvg = $customDashIcon;
    }
    ?>
    <li
        class="svg-icon-container svg-active-effects mainmenu-item <?= $isActive ? 'active' : '' ?> <?= $itemInfo->subMenuHasDropdown ? 'has-subitems' : '' ?> <?= $isDashboard ? 'is-dashboard' : '' ?>"
        data-submenu-index="<?= $itemIndex ?>">
            <?php if ($itemInfo->subMenuHasDropdown) { ?>
                <a href="#mainmenuitem<?= $itemIndex ?>" data-bs-toggle="collapse" role="button" aria-expanded="<?= $isActive ? 'true' : 'false' ?>" aria-controls="mainmenuitem<?= $itemIndex ?>">
                    <?= $this->makeLayoutPartial('invato/mainmenu_item', ['item' => $item]) ?>
                    <?php if ($itemInfo->subMenuHasDropdown) { ?>
                        <span class="submenu-indicator">
                            <i class="icon-chevron-down"></i>
                        </span>
                    <?php } ?>
                </a>
                <div class="invato-submenu collapse <?= $isActive ? 'show' : '' ?>" id="mainmenuitem<?= $itemIndex ?>">
                    <ul class="invato-submenu-items" data-submenu-index="<?= $itemIndex ?>">
                        <?= $this->makeLayoutPartial('invato/submenu_items', [
                            'sideMenuItems' => $itemInfo->subMenuItems,
                            'mainMenuItemActive' => BackendMenu::isMainMenuItemActive($itemInfo->mainMenuItem),
                            'mainMenuItemCode' => $itemInfo->mainMenuItem->code,
                            'context' => $context,
                        ]) ?>
                    </ul>
                </div>
            <?php } else { ?>
                <a href="<?= $item->url ?>">
                    <?= $this->makeLayoutPartial('invato/mainmenu_item', ['item' => $item]) ?>
                </a>
            <?php } ?>
    </li>
<?php } ?>
