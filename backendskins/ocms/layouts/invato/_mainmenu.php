<?php

use Backend\Models\BrandSetting;

$activeItem = BackendMenu::getActiveMainMenuItem();
$navbarMode = BrandSetting::get('menu_mode', BrandSetting::MENU_INLINE);
$context = BackendMenu::getContext();
$isVerticalMenu = isset($isVerticalMenu) ? $isVerticalMenu : false;
$navLogo = BrandSetting::getNavLogo();
?>
<div class="main-menu-container">
    <nav class="navbar control-toolbar navbar-mode-<?= $navbarMode ?> <?= $navLogo ? 'has-logo' : '' ?> flex" role="navigation">
        <?php if (! $isVerticalMenu && $navLogo) { ?>
            <div class="toolbar-item toolbar-logo fix-width">
                <a href="<?= Backend::url() ?>" class="mainmenu-logo <?= BackendMenu::isDashboardItemActive() ? 'active' : '' ?>">
                    <img
                        src="<?= e($navLogo) ?>"
                        alt=""
                        class="nav-logo"
                        loading="lazy"
                    />
                </a>
            </div>
        <?php } ?>
        <ul class="mainmenu-items mainmenu-general" data-main-menu>
            <?= $this->makeLayoutPartial('invato/mainmenu_items', [
                'context' => $context,
                'hideDashitem' => (bool) $navLogo,
            ]) ?>
        </ul>
    
    </nav>
</div>

