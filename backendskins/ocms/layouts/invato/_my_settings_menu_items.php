<?php
$mySettings = System\Classes\SettingsManager::instance()->listItems('mysettings');
?>
<?php foreach ($mySettings as $category => $items) { ?>
    <?php foreach ($items as $item) { ?>
        <li class="mainmenu-item" <?= $item->itemAttributes() ?>>
            <a href="<?= $item->url ?>" <?= $item->linkAttributes() ?>>
                <?= $this->makeLayoutPartial('mainmenu_item', [
                    'item' => $item,
                    'noCounter' => true,
                ]) ?>
            </a>
        </li>
    <?php } ?>
    <?php /* <li class="divider"></li> */ ?>
<?php } ?>

<li class="mainmenu-item">
    <a href="<?= Backend::url('backend/auth/signout') ?>">
        <span class="nav-icon">
            <i class="octo-icon-exit"></i>
        </span>
        <span class="nav-label">
            <?= e(trans('backend::lang.account.sign_out')) ?>
        </span>
    </a>
</li>
