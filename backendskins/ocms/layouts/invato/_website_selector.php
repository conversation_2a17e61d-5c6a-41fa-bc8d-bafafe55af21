<div class="inv-website-selector">
    <button type="button" class="inv-website-dropdown-trigger btn btn-primary" onclick="toggleDropdown()">Websites <i class="inv-dropdown-btn-icon oc-icon-chevron-down ml-3"></i><i class="inv-dropdown-btn-icon-active oc-icon-chevron-up ml-3"></i></button>

    <div class="inv-website-selector-dropdown">
        <div class="inv-search">
            <input type="text" class="inv-website-selector-search" name="invs" type="text" placeholder="Zoek website" onkeyup="filterWebsites()"> 
        </div>
        <div class="inv-website-list">
            <?php
                $sites = $this->user->access_to_websites;
                asort($sites);
            ?>
            <?php foreach ( $sites as $index => $site) { ?>
                <a href="https://<?php echo $site; ?>/backend" target="_blank" class="inv-website-selector-site">
                    <?php echo $site; ?>
                </a>
            <?php } ?>
        </div>
    </div>
</div>

<script>
    function toggleDropdown() {
        document.querySelector(".inv-website-dropdown-trigger").classList.toggle("active");
        document.querySelector(".inv-website-selector-dropdown").classList.toggle("show");
    }

    function filterWebsites() {
        var input, filter, ul, li, a, i;
        input = document.querySelector(".inv-website-selector-search");
        filter = input.value.toUpperCase();
        div = document.querySelector(".inv-website-selector-dropdown");
        a = div.getElementsByTagName("a");
        for (i = 0; i < a.length; i++) {
            txtValue = a[i].textContent || a[i].innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                a[i].style.display = "";
            } else {
                a[i].style.display = "none";
            }
        }
    }
</script>