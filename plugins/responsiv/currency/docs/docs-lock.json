{"navigation": [{"title": "Introduction", "description": "explains how to get started using the Currency plugin", "slug": "introduction"}, {"title": "Building Exchange Types", "description": "how to implement exchange integration for automated currency conversion", "slug": "building-exchange-types"}, {"title": "Services", "children": [{"title": "Currency Manager", "description": "services used to format currencies and request the active, default and primary currencies", "slug": "currency-manager"}, {"title": "Exchange Manager", "description": "manages currency conversion and drivers for exchange types", "slug": "exchange-manager"}]}], "content": {"introduction": "# Introduction\n\nThe Currency plugin brings currency management to October CMS, allowing for currency display and automatic conversion using exchange pairs, managed manually or automated via exchange providers.\n\nTo get started, we recommend installing this plugin with the `Responsiv.Agency` theme to demonstrate its functionality.\n\n- https://github.com/responsiv/agency-theme\n", "building-exchange-types": "# Building Exchange Types\r\n\r\nExchange types are currency conversion providers defined as classes located in the **exchangetypes** directory of this plugin. You can create your own plugins with this directory or place them inside the `app` directory.\r\n\r\n```\r\nplugins/\r\n  acme/\r\n    myplugin/\r\n      exchangetypes/\r\n        fixer/           <=== Class Directory\r\n          fields.yaml    <=== Field Configuration\r\n        Fixer.php        <=== Class File\r\n      Plugin.php\r\n```\r\n\r\nThese instructions can be used to create your own exchange type classes to integrate with specific gateways, and a plugin can be host to many exchange types, not just one.\r\n\r\n## Payment Type Definition\r\n\r\nPayment type classes should extend the `Responsiv\\Currency\\Classes\\ExchangeBase` class, which is an abstract PHP class containing all the necessary methods for implementing a exchange type. By extending this base class, you can add the necessary features, such as communicating with the the currency converter API.\r\n\r\nThe exchange type from the next example should be defined in the **plugins/acme/myplugin/exchangetypes/Fixer.php** file. Aside from the PHP file, exchange types can also have a directory that matches the PHP file name. If the class name is **Fixer.php** then the corresponding directory name is **fixer**. These class directories can contain partials and form field configuration used by the exchange type.\r\n\r\n```php\r\nclass Fixer extends GatewayBase\r\n{\r\n    public function driverDetails()\r\n    {\r\n        return [\r\n            'name' => 'Fixer',\r\n            'description' => 'Currency exchange rate service provided by Fixer.io'\r\n        ];\r\n    }\r\n\r\n    public function getExchangeRate($fromCurrency, $toCurrency)\r\n    {\r\n        // ...\r\n    }\r\n}\r\n```\r\n\r\nThe `driverDetails` method is required. The method should return an array with two keys: name and description. The name and description are display in the administration panel when setting up the exchange type.\r\n\r\nPayment types must be registered by overriding the `registerCurrencyConverters` method inside the plugin registration file (Plugin.php). This tells the system about the exchange type and provides a short code for referencing it.\r\n\r\nThe following registers the `Fixer` class with the code **fixer* so it is ready to use.\r\n\r\n```php\r\npublic function registerCurrencyConverters()\r\n{\r\n    return [\r\n        \\Responsiv\\Pay\\PaymentTypes\\Fixer::class => 'fixer',\r\n    ];\r\n}\r\n```\r\n\r\n## Building the Payment Configuration Form\r\n\r\nBy default, the exchange type will look for its form field definitions as a file **fields.yaml** in the class directory. In this file, you can define [form fields and tabs](https://docs.octobercms.com/3.x/element/form-fields.html) used by the exchange type configuration form.\r\n\r\nWhen a exchange type is selected for configuration, it is stored as a `Responsiv\\Pay\\Models\\PaymentMethod` instance. All field values are saved automatically to this model and are available inside the processing code.\r\n\r\nThe configuration form fields can add things like API usernames and passwords used for the payment gateway. The following might be stored in the **plugins/acme/myplugin/exchangetypes/fixer/fields.yaml** file.\r\n\r\n```yaml\r\nfields:\r\n    access_key:\r\n        label: API Access Key\r\n        comment: Specify the unique key assigned to your Fixer account.\r\n        span: auto\r\n        tab: Configuration\r\n\r\n    use_secure_endpoint:\r\n        label: Use Secure Endpoint\r\n        comment: Some subscriptions do not support HTTPS so uncheck this to disable encryption.\r\n        type: checkbox\r\n        tab: Configuration\r\n```\r\n\r\n### Initializing the Configuration Form\r\n\r\nYou may initialize the values of the configuration form fields by overriding the `initDriverHost` method in the exchange type class definition. The method takes a `$host` argument as the model object, which can be used to set the attribute values matching the form fields.\r\n\r\nThe following example checks if the model is newly created using `$host->exists` and sets some default values.\r\n\r\n```php\r\npublic function initDriverHost($host)\r\n{\r\n    if (!$host->exists) {\r\n        $host->name = 'Fixer';\r\n        $host->use_secure_endpoint = false;\r\n    }\r\n}\r\n```\r\n\r\n### Validating the Configuration Form\r\n\r\nOnce you have the form fields specified, you may wish to validate their input. Override the `validateDriverHost` method inside the class to implement validation logic. The method takes a `$host` argument as a model object with the attributes matching those found in the form field definition.\r\n\r\nThrow the `\\ValidationException` exception to trigger a validation error message. This exception takes an array with the field name as a key and the error message as the value. The message should use the `__()` helper function to enable localization for the message.\r\n\r\n```php\r\npublic function validateDriverHost($host)\r\n{\r\n    if (!$host->access_key) {\r\n        throw new \\ValidationException(['access_key' => __(\"Please specify an Access Key\")]);\r\n    }\r\n}\r\n```\r\n\r\nFor simple validation rules, you can apply them to the model using the `initDriverHost` method.\r\n\r\n```php\r\npublic function initDriverHost($host)\r\n{\r\n    $host->rules['access_key'] = 'required';\r\n}\r\n```\r\n\r\n## Requesting Exchange Rates from a Provider\r\n\r\nWhen the currency plugin needs to find an exchange rate, it invokes the `getExchangeRate` method of a corresponding exchange type class. This method sends a request to the service provider to request the latest rate, using the source and destination currency, along with any API keys to authenticate the request.\r\n\r\nThe method should be defined in the following way and takes two arguments, the source `$fromCurrency` and destination `$toCurrency` passed as currency codes. For example, from **USD** to **AUD**.\r\n\r\n```php\r\npublic function getExchangeRate($fromCurrency, $toCurrency)\r\n{\r\n    // ...\r\n}\r\n```\r\n\r\nThe contents of the `getExchangeRate` depends on the specific exchange provider's requirements, however the method should integrate with the API documentation provided. If an error occurs, the `\\SystemException` can be thrown to log an invalid responses, or the `\\ApplicationException` can be used for unlogged errors.\r\n\r\n```php\r\npublic function getExchangeRate($fromCurrency, $toCurrency)\r\n{\r\n    $fromCurrency = trim(strtoupper($fromCurrency));\r\n    $toCurrency = trim(strtoupper($toCurrency));\r\n\r\n    $response = $this->requestRatesFromService($fromCurrency);\r\n    if (!$response) {\r\n        throw new \\SystemException('Error loading the Fixer currency exchange feed.');\r\n    }\r\n\r\n    $rates = $response['rates'] ?? [];\r\n    if (!$rates) {\r\n        throw new \\SystemException('The Fixer currency exchange rate service returned invalid data.');\r\n    }\r\n\r\n    if (!$rate = array_get($rates, $toCurrency)) {\r\n        throw new \\SystemException('The Fixer currency exchange rate service is missing the destination currency.');\r\n    }\r\n\r\n    return $rate;\r\n}\r\n```\r\n\r\nIf the exchange rate should not provide any currency exchange data, for example, when the currency pair is not supported, it can return the `\\Responsiv\\Currency\\Models\\ExchangeConverter::NO_RATE_DATA` constant to gracefully inform the process that no exchange rate was found for the requested pair.\r\n\r\n```php\r\npublic function getExchangeRate($fromCurrency, $toCurrency)\r\n{\r\n    return \\Responsiv\\Currency\\Models\\ExchangeConverter::NO_RATE_DATA;\r\n}\r\n```\r\n", "currency-manager": "# Currency Manager\r\n\r\nThere is a `Currency` facade you may use for common currency management tasks. This facade resolves to the `Responsiv\\Currency\\Classes\\CurrencyManager` instance by default.\r\n\r\nYou may use the `convert` method on the `Currency` facade to convert a currency value.\r\n\r\n```php\r\n// Converts the default currency to AUD\r\nCurrency::convert(100, 'AUD');\r\n\r\n// Converts explicitly the USD currency to AUD\r\nCurrency::convert(100, 'AUD', 'USD');\r\n```\r\n\r\nThe `getDefault` returns the default currency model, and `getDefaultCode` returns the default currency code.\r\n\r\n```php\r\n$default = Currency::getDefault();\r\n\r\n$defaultCode = Currency::getDefaultCode();\r\n```\r\n\r\nThe `getPrimary` returns the default currency model, and `getPrimaryCode` returns the default currency code.\r\n\r\n```php\r\n$primary = Currency::getPrimary();\r\n\r\n$primaryCode = Currency::getPrimaryCode();\r\n```\r\n\r\nThe `getActive` returns the default currency model, and `getActiveCode` returns the default currency code.\r\n\r\n```php\r\n$active = Currency::getActive();\r\n\r\n$activeCode = Currency::getActiveCode();\r\n```\r\n\r\n## Currency Model\r\n\r\nA currency object represents a `Responsiv\\Currency\\Models\\Currency` model object.\r\n\r\nThe `fromBaseValue` converts a currency, for example, converts 100 to 1.00.\r\n\r\n```php\r\n// Returns 1.00\r\n$currency->fromBaseValue(100);\r\n```\r\n\r\nThe `toBaseValue` converts a currency to a base value, for example, converts 1.00 to 100.\r\n\r\n```php\r\n// Returns 100\r\n$currency->toBaseValue(1.00);\r\n```\r\n", "exchange-manager": "# Exchange Manager\r\n\r\nThe `Responsiv\\Currency\\Classes\\ExchangeManager` instance is used to manage currency exchange and conversion. Use the `instance` method to create an instance of the exchange manager.\r\n\r\n```php\r\n$manager = ExchangeManager::instance();\r\n```\r\n\r\n## Currency Conversion\r\n\r\nCurrency converters are registered as exchange types the `registerCurrencyConverters` method override in the plugin registration file.\r\n\r\n- [Learn more about Building Exchange Types](./building-exchange-types.md)\r\n\r\n## Currency Exchange\r\n\r\nThe `getRate` method will return an exchange rate for a currency pair, containing a `$fromCurrency` and `$toCurrency`. For example, the exchange rate from USD to AUD. If an exchange rate is not found, the reverse conversion will be attempted.\r\n\r\n```php\r\n// Returns the exchange rate from USD to AUD\r\n$manager->getRate('USD', 'AUD');\r\n```\r\n\r\nThe `requestAllRates` will spin over every configured currency converter and request the most recent rates. Pairs are stored against the `Responsiv\\Currency\\Models\\ExchangeRate` model.\r\n\r\n```php\r\n$manager->requestAllRates();\r\n```\r\n"}}