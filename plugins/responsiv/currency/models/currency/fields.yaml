# ===================================
#  Form Field Definitions
# ===================================

tabs:
    defaultTab: Currency
    fields:
        code:
            label: Currency Code
            comment: International currency code, e.g. USD
            span: left

        name:
            label: Name
            comment: Human readable name
            span: full

        is_enabled:
            label: Enabled
            comment: Make currency available.
            type: checkbox
            span: auto
            default: true

        is_default:
            label: Default
            comment: Use this as the default currency.
            type: checkbox
            span: auto
            default: true

        currency_symbol:
            label: Symbol
            comment: Symbol to put beside amount, e.g. $
            tab: Formatting
            span: left

        thousand_separator:
            label: Thousand Separator
            comment: Character to separate thousands
            tab: Formatting
            span: auto

        decimal_point:
            label: Decimal Point
            comment: Character to use as decimal point
            tab: Formatting
            span: auto

        decimal_scale:
            label: Decimal Scale
            comment: Size of the decimal number after the decimal point, e.g. 2
            type: number
            tab: Formatting
            span: auto
            default: 2

        place_symbol_before:
            label: Place symbol before number
            type: checkbox
            span: full
            tab: Formatting
