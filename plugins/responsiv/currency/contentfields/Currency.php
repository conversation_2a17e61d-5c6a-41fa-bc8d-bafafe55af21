<?php namespace Responsiv\Currency\ContentFields;

use Site;
use Currency as CurrencyService;
use Tailor\Classes\ContentFieldBase;
use October\Contracts\Element\FormElement;
use October\Contracts\Element\ListElement;

/**
 * Currency Content Field
 */
class Currency extends ContentFieldBase
{
    /**
     * defineForm<PERSON>ield will define how a field is displayed in a form.
     */
    public function defineFormField(FormElement $form, $context = null)
    {
        $form->addFormField($this->fieldName, $this->label)->useConfig($this->config)->displayAs('currency');
    }

    /**
     * defineListColumn will define how a field is displayed in a list.
     */
    public function defineListColumn(ListElement $list, $context = null)
    {
        $useSite = $this->getDefaultColumnSite($list);

        $column = $list->defineColumn($this->fieldName, $this->label)->displayAs('currency')->site($useSite);
        if ($this->column) {
            $column->useConfig($this->column);
        }
    }

    /**
     * defineBatchListColumn
     */
    public function defineBatchListColumn(ListElement $list, $context = null)
    {
        $list->defineColumn($this->fieldName, $this->label);
    }

    /**
     * extendBatchModelObject
     */
    public function extendBatchModelObject($model)
    {
        if ($model instanceof \Tailor\Models\RecordExport) {
            $model->bindEvent('model.beforeExportAttribute', function ($attr, &$value) {
                if ($attr === $this->fieldName) {
                    $value = CurrencyService::getForModel($this->model, $attr)->toFloatValue($value);
                }
            });
        }

        if ($model instanceof \Tailor\Models\RecordImport) {
            $model->bindEvent('model.beforeImportAttribute', function ($attr, &$value) {
                if ($attr === $this->fieldName) {
                    $value = CurrencyService::getForModel($this->model, $attr)->fromFloatValue($value);
                }
            });
        }
    }

    /**
     * extendDatabaseTable adds any required columns to the database.
     */
    public function extendDatabaseTable($table)
    {
        $table->bigInteger($this->fieldName)->nullable();
    }

    /**
     * getDefaultColumnSite returns true if the model and field name uses multisite
     */
    protected function getDefaultColumnSite($list)
    {
        if ($this->site !== null) {
            return $this->site;
        }

        if (!$model = $list->{'getModel'}()) {
            return null;
        }

        if (Site::isModelMultisite($model, $this->fieldName)) {
            return true;
        }

        return false;
    }
}
