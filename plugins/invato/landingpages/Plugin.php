<?php namespace Invato\Landingpages;

use System\Classes\PluginBase;
use Event;
use Invato\Landingpages\Models\LandingPage;
use Invato\Traits\RegistersPageFinderTrait;

/**
 * Plugin class
 */
class Plugin extends PluginBase
{
    use RegistersPageFinderTrait;

    public function boot(): void
    {
        $this->registerPageFinder();
    }

    public function register(): void {}

    public function registerComponents(): array
    {
        return [
            \Invato\Landingpages\Components\RenderLandingPage::class => 'renderLandingPage'
        ];
    }

    /**
     * Get PageFinder configuration for this plugin
     */
    protected function getPageFinderConfig(): array
    {
        return [
            'model' => LandingPage::class,
            'menu_types' => [
                'landingpage' => 'invato.landingpages::lang.menuitem.landingpage',
            ],
        ];
    }
}
