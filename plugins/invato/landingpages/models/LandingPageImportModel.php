<?php

namespace Invato\Landingpages\Models;

use Backend\Models\ImportModel;
use Exception;
use Invato\Landingpages\Models\LandingPage;

class LandingPageImportModel extends ImportModel
{
    public $rules = [
        'title' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_landingpages_pages,slug,{{id}}'],
        'page_url' => ['required', 'string', 'max:255'],
    ];

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if landing page with this title/slug already exists
                $slug = $data['slug'] ?? str_slug($data['title']);
                $landingPage = LandingPage::withTrashed()->where('slug', $slug)->first();

                if (! $landingPage) {
                    $landingPage = new LandingPage;
                }

                $landingPage->fill([
                    'title' => $data['title'] ?? null,
                    'slug' => $slug ?? null,
                    'page_url' => $data['page_url'] ?? null,
                ]);

                $landingPage->save();

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
