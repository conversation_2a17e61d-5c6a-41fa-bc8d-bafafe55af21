<?php namespace Invato\Landingpages\Models;

use Model;
use Invato\Landingpages\Components\RenderLandingPage;
use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Traits\HasPageFinderTrait;

/**
 * Model
 */
class LandingPage extends Model
{
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use \October\Rain\Database\Traits\Validation;
    use \October\Rain\Database\Traits\SoftDelete;
    use \October\Rain\Database\Traits\Sluggable;
    use \October\Rain\Database\Traits\Sortable;

    protected static function booted(): void
    {
        static::deleting(static function ($landingpage) {
            static::createRedirect(
                plugin: 'landingpages',
                modelRecord: $landingpage,
                detailPageController: RenderLandingPage::class,
                status: 301
            );
        });

        static::restored(static function ($landingpage) {
            static::deleteRedirect($landingpage);
        });
    }

    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
        'slug' => 'string',
        'page_url' => 'string',
    ];

    protected $fillable = [
        'title',
        'slug',
        'page_url'
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'title' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_landingpages_pages,slug,{{id}}'],
        'page_url' => ['required', 'string', 'max:255'],
    ];

    protected $slugs = ['slug' => 'title'];


    /**
     * @var array dates to cast from the database.
     */
    protected $dates = ['deleted_at'];

    /**
     * @var string table in the database used by the model.
     */
    public $table = 'invato_landingpages_pages';

    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'landingpage',
            'all_type' => 'all-landingpages',
            'component' => 'renderLandingPage',
        ];
    }

}
