<?php

namespace Invato\Landingpages\Models;

use Backend\Models\ExportModel;
use JsonException;

class LandingPageExportModel extends ExportModel
{
    /**
     * @throws JsonException
     */
    public function exportData($columns, $sessionKey = null): array
    {
        $landingPages = LandingPage::with('categories')->get();
        $exportData = [];

        foreach ($landingPages as $landingPage) {
            $record = [];

            foreach ($columns as $column) {
                // Handle special cases
                match ($column) {
                    'categories' => $record[$column] = $landingPage->categories->pluck('title')->implode(', '),
                    'images',
                    'usps' => $record[$column] = is_array($landingPage->{$column})
                        ? implode(', ', $landingPage->{$column})
                        : $landingPage->{$column},
                    'specifications',
                    'custom_options' => $record[$column] = json_encode($landingPage->{$column},
                        JSON_THROW_ON_ERROR),
                    default => $record[$column] = $landingPage->{$column},
                };
            }

            $exportData[] = $record;
        }

        return $exportData;
    }
}
