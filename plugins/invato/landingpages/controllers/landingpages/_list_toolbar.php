<?php
    use Backend\Facades\BackendAuth;

    $pluginUri = 'landingpages';
    $modelUri = 'landingpages';
?>

<div data-control="toolbar">
    <!-- Create button -->
    <button
        type="button"
        data-control="popup"
        data-handler="onLoadPopupForm"
        class="btn btn-primary oc-icon-plus">
        <?= e(trans('backend::lang.form.create')) ?>
    </button>

    <button
        class="btn btn-default oc-icon-trash-o"
        data-request="onDelete"
        data-request-confirm="<?= e(trans('backend::lang.list.delete_selected_confirm')) ?>"
        data-list-checked-trigger
        data-list-checked-request
        data-stripe-load-indicator>
        <?= e(trans('backend::lang.list.delete_selected')) ?>
    </button>

    <?php if (BackendAuth::getUser()->hasAccess("invato.{$pluginUri}.export_{$modelUri}")) { ?>
        <!-- Export button -->
        <a
            href="<?= Backend::url("invato/{$pluginUri}/{$modelUri}/export") ?>"
            class="btn btn-default oc-icon-download">
            <?= e(trans('Export')) ?>
        </a>
    <?php } ?>

    <?php if (BackendAuth::getUser()->hasAccess("invato.{$pluginUri}.import_{$modelUri}")) { ?>
        <!-- Import button -->
        <a
            href="<?= Backend::url("invato/{$pluginUri}/{$modelUri}/import") ?>"
            class="btn btn-default oc-icon-upload">
            <?= e(trans('Import')) ?>
        </a>
    <?php } ?>

    <!-- Delete selected button -->
    <button
        class="btn btn-danger oc-icon-trash-o"
        data-request="onDelete"
        data-request-confirm="<?= e(trans('Delete selected rows')) ?>"
        data-list-checked-trigger
        data-list-checked-request
        data-stripe-load-indicator>
        <?= e(trans('Delete selected')) ?>
    </button>

    <!-- Restore selected button -->
    <button
        class="btn btn-success oc-icon-refresh"
        data-request="onRestoreSelected"
        data-request-confirm="<?= e(trans('Restore selected rows')) ?>"
        data-list-checked-trigger
        data-list-checked-request
        data-stripe-load-indicator>
        <?= e(trans('Restore selected')) ?>
    </button>
</div>
