<?php

namespace Invato\Landingpages\Controllers;

use BackendMenu;
use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use Invato\Landingpages\Models\LandingPage;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;

class LandingPages extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static string $modelClass = LandingPage::class;

    public string $formConfig = 'config_form.yaml';

    public string $listConfig = 'config_list.yaml';

    public string $importExportConfig = 'config_import_export.yaml';

    public string $importPermission = 'invato.landingpages.import_pages';

    public string $exportPermission = 'invato.landingpages.export_pages';

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Landingpages', 'landingpages', 'landingpage');
    }

}
