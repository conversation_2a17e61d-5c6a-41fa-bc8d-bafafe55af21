<?php

namespace Invato\Blog\Models;

use Backend\Facades\BackendAuth;
use Cms\Classes\Controller;
use Cms\Classes\Page as CmsPage;
use Cms\Classes\Theme;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Invato\Blog\Components\PostDetail;
use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Seo\traits\HasSeoableTrait;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;
use RainLab\Translate\Behaviors\TranslatableModel;

class Post extends Model
{
    // Begin Skeleton model
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use HasSeoableTrait;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function ($post) {
            static::createRedirect(
                plugin: 'blog',
                modelRecord: $post,
                detailPageController: PostDetail::class,
                status: 301
            );
        });

        static::restored(static function ($post) {
            static::deleteRedirect($post);
        });
    }

    public $table = 'invato_blog_post';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'author_id' => 'integer',
        'title' => 'string',
        'slug' => 'string',
        'status' => 'string',
        'publication_date' => 'datetime',
        'title_short' => 'string',
        'excerpt' => 'string',
        'content' => 'string',
        'sort_order' => 'integer',
        'is_featured' => 'boolean',
        'img_title' => 'string',
        'thumb_img_title' => 'string',
        'cta_title' => 'string',
        'cta_content' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'author_id',
        'title',
        'slug',
        'status',
        'publication_date',
        'title_short',
        'excerpt',
        'content',
        'sort_order',
        'is_featured',
        'additional_content',
        'img_title',
        'thumb_img_title',
        'cta_title',
        'cta_content',
        'cta_button',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'title' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_blog_post,slug,{{id}}'],
        'status' => ['required', 'string', 'max:255'],
        'content' => ['required', 'string'],
        'title_short' => ['nullable', 'string', 'max:255'],
        'excerpt' => ['nullable', 'string'],
        'publication_date' => ['nullable', 'date'],
        'sort_order' => ['integer'],
        'is_featured' => ['boolean'],
        'img_title' => ['nullable', 'string', 'max:255'],
        'thumb_img_title' => ['nullable', 'string', 'max:255'],
        'cta_title' => ['nullable', 'string', 'max:255'],
        'cta_content' => ['nullable', 'string'],
    ];

    // translatable
    public $implement = [
        TranslatableModel::class,
    ];

    public $translatable = [
        'title',
        'slug',
        'content',
        'title_short',
        'excerpt',
        'additional_content',
        'img_title',
        'thumb_img_title',
        'cta_title',
        'cta_content',
        'cta_button',
    ];

    protected array $slugs = [
        'slug' => 'title',
    ];

    // These attributes should not be in $casts and $rules
    protected $jsonable = [
        'additional_content',
        'cta_button',
    ];

    public $belongsTo = [
        'author' => Author::class,
    ];

    public $belongsToMany = [
        'categories' => [Category::class, 'table' => 'invato_blog_cat_post'],
    ];

    public $mediaAttributes = [
        'image',
        'thumbnail_image',
    ];

    /**
     * Get PageFinder configuration for Post model
     */
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'blog-post',
            'all_type' => 'all-blog-posts',
            'component' => 'PostDetail',
        ];
    }

    // END Skeleton model

    // BEGIN Model specific

    public function scopePublished(Builder $query): void
    {
        if (BackendAuth::getUser()) {
            return;
        }

        $query->where('status', '=', 'published');
    }

    public function readingTime(): Attribute
    {
        return new Attribute(
            get: fn ($value) => $this->calculateReadingTime(),
        );
    }

    public function wordCount(): Attribute
    {
        return new Attribute(
            get: fn ($value) => $this->calculateWordCount(),
        );
    }

    public function tableOfContents(): Attribute
    {
        return new Attribute(
            get: fn ($value) => $this->generateTableOfContents(),
        );
    }



    private function calculateReadingTime(): int
    {
        // Strip HTML tags and calculate word count
        $cleanContent = strip_tags($this->getFullContent());
        $wordCount = str_word_count($cleanContent);

        // Define average reading speed in words per minute (200 WPM from the referenced method)
        $averageReadingSpeed = 200;

        // Convert reading speed to words per second
        $wordsPerSecond = $averageReadingSpeed / 60;

        // Calculate reading time in exact seconds and round up
        return ceil($wordCount / $wordsPerSecond);
    }

    private function calculateWordCount(): int
    {
        return str_word_count(strip_tags($this->getFullContent()));
    }

    private function getFullContent(): string
    {
        $content = '';
        if (isset($this->excerpt)) {
            $content .= $this->excerpt.' ';
        }

        $content .= $this->content.' ';

        if (isset($this->additional_content)) {
            foreach ($this->additional_content as $additionalContent) {
                if (isset($additionalContent['text'])) {
                    $content .= $additionalContent['text'].' ';
                }

                if (isset($additionalContent['cta']['title'])) {
                    $content .= $additionalContent['cta']['title'].' ';
                }

                if (isset($additionalContent['cta']['content'])) {
                    $content .= $additionalContent['cta']['content'].' ';
                }
            }
        }

        return $content;
    }

    private function generateTableOfContents(): array
    {
        $content = $this->getContentForToc();
        $toc = [];

        if (empty($content)) {
            return $toc;
        }

        // Create a DOMDocument to parse HTML
        $dom = new \DOMDocument();

        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);

        // Load the HTML content with proper encoding
        $dom->loadHTML('<?xml encoding="UTF-8">' . '<div>' . $content . '</div>', LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        // Clear any libxml errors
        libxml_clear_errors();

        // Find all heading tags (h1, h2, h3, h4)
        $xpath = new \DOMXPath($dom);
        $headings = $xpath->query('//h1 | //h2 | //h3 | //h4');

        foreach ($headings as $heading) {
            $text = trim($heading->textContent);
            if (!empty($text)) {
                $level = (int) substr($heading->tagName, 1); // Extract number from h1, h2, etc.
                $id = $this->generateHeadingId($text);

                $toc[] = [
                    'text' => $text,
                    'level' => $level,
                    'id' => $id,
                ];
            }
        }

        return $toc;
    }

    private function getContentForToc(): string
    {
        $content = '';

        // Include main content
        if (isset($this->content)) {
            $content .= $this->content;
        }

        // Include additional content sections
        if (isset($this->additional_content)) {
            foreach ($this->additional_content as $additionalContent) {
                // Include text content
                if (isset($additionalContent['text'])) {
                    $content .= $additionalContent['text'];
                }

                // Include CTA content
                if (isset($additionalContent['cta'])) {
                    $cta = $additionalContent['cta'];

                    // Add CTA title (H2 by default)
                    if (isset($cta['title']) && !empty($cta['title'])) {
                        $content .= '<h2>' . $cta['title'] . '</h2>';
                    }

                    // Add CTA content (may contain additional headings)
                    if (isset($cta['content']) && !empty($cta['content'])) {
                        $content .= $cta['content'];
                    }
                }
            }
        }

        return $content;
    }

    private function generateHeadingId(string $text): string
    {
        // Convert to lowercase and replace spaces/special chars with hyphens
        $id = strtolower($text);
        $id = preg_replace('/[^a-z0-9\s-]/', '', $id);
        $id = preg_replace('/[\s-]+/', '-', $id);
        $id = trim($id, '-');

        return $id ?: 'heading-' . uniqid();
    }



    public static function getMenuTypeInfo($type)
    {
        $result = [];

        if ($type == 'blog-post') {
            $references = [];

            $posts = self::orderBy('title')->get();
            foreach ($posts as $post) {
                $references[$post->id] = $post->title;
            }

            $result = [
                'references' => $references,
                'nesting' => false,
                'dynamicItems' => false,
            ];
        }

        if ($type == 'all-blog-posts') {
            $result = [
                'nesting' => true,
                'dynamicItems' => true,
            ];
        }

        if ($result) {
            $theme = Theme::getActiveTheme();

            $pages = CmsPage::listInTheme($theme, true);
            $cmsPages = [];

            foreach ($pages as $page) {
                if (! $page->hasComponent('postDetail')) {
                    continue;
                }

                $properties = $page->getComponentProperties('postDetail');
                if (! preg_match('/{{\s*:/', $properties['slug'])) {
                    continue;
                }

                $cmsPages[] = $page;
            }

            $result['cmsPages'] = $cmsPages;
        }

        return $result;
    }

    public static function resolveMenuItem($item, $url, $theme)
    {
        $result = null;

        if ($item->type == 'blog-post') {
            $model = Post::find($item->reference);

            if (! $model) {
                return;
            }

            $controller = new Controller($theme);
            $pageUrl = $controller->pageUrl($item->cmsPage, [
                'id' => $model->id,
                'slug' => $model->slug,
            ]);

            $result = [
                'url' => $pageUrl,
                'isActive' => $pageUrl == $url,
                'title' => $model->title,
                'mtime' => $model->updated_at,
            ];

            return $result;
        } elseif ($item->type == 'all-blog-posts') {
            $result = [
                'items' => [],
            ];

            $posts = self::orderBy('title')->get();
            $controller = new Controller($theme);

            foreach ($posts as $post) {
                $pageUrl = $controller->pageUrl($item->cmsPage, [
                    'id' => $post->id,
                    'slug' => $post->slug,
                ]);

                $postItem = [
                    'title' => $post->title,
                    'url' => $pageUrl,
                    'mtime' => $post->updated_at,
                ];

                $postItem['isActive'] = $postItem['url'] == $url;

                $result['items'][] = $postItem;
            }
        }

        return $result;
    }

    // Define the observer logic
    public static function boot()
    {
        parent::boot();

        // Register a saving observer
        static::saving(function ($post) {
            // If the model is set to featured
            if ($post->is_featured) {
                // Set all other models' featured field to false
                self::where('is_featured', true)->update(['is_featured' => false]);
            }
        });
    }

    public function beforeSave()
    {
        if ($this->is_featured) {
            Post::where('is_featured', true)->update(['is_featured' => false]);
        }
    }

}
