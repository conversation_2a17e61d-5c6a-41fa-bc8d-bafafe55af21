cleanup() {
    echo ""
    echo "Stopping installation"
    exit 1
}

trap cleanup INT

# styling variables
RED='\033[0;31m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# check if Herd Pro is active
herd --version > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "Herd Pro is niet actief of niet ge<PERSON>."
    exit 1
fi

# copy .env.example to .env
if [ ! -f .env ]; then
    cp .env.example .env
fi

# wait for filesystem
sleep 0.5

# set .env variable based on current project
# get current directory
appCurrentDirectory=$(basename $(pwd))

# app url
appHost="${appCurrentDirectory}.test"

set_env_variable() {
    key=$1
    value=$2

    sed -i '' -e "s|${key}=.*|${key}=${value}|" .env
    echo "${RED}${key}${NC} set to: ${CYAN}${value}${NC}"
    sleep 0.2
}

# set all .env variables
set_env_variable "APP_NAME" "${appCurrentDirectory}"
set_env_variable "APP_URL" "https://${appHost}"
set_env_variable "DB_HOST" 127.0.0.1
set_env_variable "DB_DATABASE" "${appCurrentDirectory}"
set_env_variable "DB_USERNAME" "root"
set_env_variable "DB_PASSWORD" "null"
set_env_variable "REDIS_HOST" 127.0.0.1
set_env_variable "MAIL_MAILER" "smtp"
set_env_variable "MAIL_HOST" 127.0.0.1
set_env_variable "MAIL_PORT" "2525"
set_env_variable "MAIL_USERNAME" "${appCurrentDirectory}"
set_env_variable "MAIL_PASSWORD" "null"
set_env_variable "MAIL_ENCRYPTION" "null"
set_env_variable "MAIL_FROM_NAME" "${appCurrentDirectory}"
set_env_variable "MAIL_FROM_ADDRESS" "ocms@${appHost}"
set_env_variable "LINK_POLICY" "secure"

# Copy herd-base.yml to herd.yml
cp herd-base.yml herd.yml

# Replace the name on the first line in herd.yml
sed -i '' -e "s|^name: .*|name: ${appCurrentDirectory}|" herd.yml

# via sub query
(
    herd init
)

# Check if database exists
DB_CHECK=$(mysql -u root --password= -h 127.0.0.1 -P 3306 -e "SHOW DATABASES LIKE '${appCurrentDirectory}';")

if [ -z "$DB_CHECK" ]; then
    echo "Creating database '${appCurrentDirectory}'..."
    mysql -u root --password= -h 127.0.0.1 -P 3306 -e "CREATE DATABASE \`${appCurrentDirectory}\`;"
    echo "Database '${appCurrentDirectory}' created successfully."
else
    echo "${RED}Error:${NC} Database '${appCurrentDirectory}' already exists."
fi

# via sub query
(
    herd composer install
    herd php artisan key:generate
    herd php artisan october:migrate
)

url="https://${appCurrentDirectory}.test"
mysql="mysql://root:@127.0.0.1:3306/${appCurrentDirectory}?statusColor=007F3D&env=local&name=localhost%20${appCurrentDirectory}&tLSMode=0&usePrivateKey=false&safeModeLevel=0&advancedSafeModeLevel=0&driverVersion=0&lazyload=false"

# show correct info:
echo "+---------------+------------------+"
echo "| ${RED}URL${NC}         | ${CYAN}${url}${NC}"
echo "| ${RED}Backend URL${NC} | ${CYAN}${url}/backend${NC}"
echo "| ${RED}TablePlus${NC}   | ${CYAN}${mysql}${NC}"
echo "+---------------+------------------+"
