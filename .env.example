APP_NAME="Invato CMS"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost
APP_LOCALE=nl

# Docker Invato stuff
APP_SERVICE='invato-basis'
TRAEFIK_HOST=
FORWARD_DB_PORT=

PLUGINS_PATH=market

ACTIVE_THEME=ocms-basis-child
BACKEND_URI=/backend
CMS_ROUTE_CACHE=false
CMS_ASSET_CACHE=false
LINK_POLICY=detect
DEFAULT_FILE_MASK=775
DEFAULT_FOLDER_MASK=775

BOXES_MAIN_MENU_ORDER=200

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=
DB_USERNAME=sail
DB_PASSWORD=password

LOG_CHANNEL=single
BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=null
MAIL_PORT=null
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=null
MAIL_REPLY_TO=null
MAIL_REPLY_TO_NAME=null
MAILGUN_DOMAIN=null
MAILGUN_SECRET=null
MAILGUN_ENDPOINT=api.eu.mailgun.net

INTRANET_API_BASE_URI="https://api.intranet.invato.nl/api/website-authentication/"
INTRANET_API_TOKEN=""
INTRANET_API_MARKET_URI="https://api.intranet.invato.nl/api/website-market/"

OCMS_DOMAIN=
